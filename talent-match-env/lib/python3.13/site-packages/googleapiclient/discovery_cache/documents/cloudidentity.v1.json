{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-identity.devices": {"description": "Private Service: https://www.googleapis.com/auth/cloud-identity.devices"}, "https://www.googleapis.com/auth/cloud-identity.devices.lookup": {"description": "See your device details"}, "https://www.googleapis.com/auth/cloud-identity.devices.readonly": {"description": "Private Service: https://www.googleapis.com/auth/cloud-identity.devices.readonly"}, "https://www.googleapis.com/auth/cloud-identity.groups": {"description": "See, change, create, and delete any of the Cloud Identity Groups that you can access, including the members of each group"}, "https://www.googleapis.com/auth/cloud-identity.groups.readonly": {"description": "See any Cloud Identity Groups that you can access, including group members and their emails"}, "https://www.googleapis.com/auth/cloud-identity.inboundsso": {"description": "See and edit all of the Inbound SSO profiles and their assignments to any Org Units or Google Groups in your Cloud Identity Organization."}, "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly": {"description": "See all of the Inbound SSO profiles and their assignments to any Org Units or Google Groups in your Cloud Identity Organization."}, "https://www.googleapis.com/auth/cloud-identity.policies": {"description": "See and edit policies in your Cloud Identity Organization."}, "https://www.googleapis.com/auth/cloud-identity.policies.readonly": {"description": "See policies in your Cloud Identity Organization."}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://cloudidentity.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Identity", "description": "API for provisioning and managing identity resources.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/identity/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudidentity:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudidentity.mtls.googleapis.com/", "name": "cloudidentity", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"customers": {"resources": {"userinvitations": {"methods": {"cancel": {"description": "Cancels a UserInvitation that was already sent.", "flatPath": "v1/customers/{customersId}/userinvitations/{userinvitationsId}:cancel", "httpMethod": "POST", "id": "cloudidentity.customers.userinvitations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `UserInvitation` name in the format `customers/{customer}/userinvitations/{user_email_address}`", "location": "path", "pattern": "^customers/[^/]+/userinvitations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelUserInvitationRequest"}, "response": {"$ref": "Operation"}}, "get": {"description": "Retrieves a UserInvitation resource. **Note:** New consumer accounts with the customer's verified domain created within the previous 48 hours will not appear in the result. This delay also applies to newly-verified domains.", "flatPath": "v1/customers/{customersId}/userinvitations/{userinvitationsId}", "httpMethod": "GET", "id": "cloudidentity.customers.userinvitations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `UserInvitation` name in the format `customers/{customer}/userinvitations/{user_email_address}`", "location": "path", "pattern": "^customers/[^/]+/userinvitations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "UserInvitation"}}, "isInvitableUser": {"description": "Verifies whether a user account is eligible to receive a UserInvitation (is an unmanaged account). Eligibility is based on the following criteria: * the email address is a consumer account and it's the primary email address of the account, and * the domain of the email address matches an existing verified Google Workspace or Cloud Identity domain If both conditions are met, the user is eligible. **Note:** This method is not supported for Workspace Essentials customers.", "flatPath": "v1/customers/{customersId}/userinvitations/{userinvitationsId}:isInvitableUser", "httpMethod": "GET", "id": "cloudidentity.customers.userinvitations.isInvitableUser", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `UserInvitation` name in the format `customers/{customer}/userinvitations/{user_email_address}`", "location": "path", "pattern": "^customers/[^/]+/userinvitations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:isInvitableUser", "response": {"$ref": "IsInvitableUserResponse"}}, "list": {"description": "Retrieves a list of UserInvitation resources. **Note:** New consumer accounts with the customer's verified domain created within the previous 48 hours will not appear in the result. This delay also applies to newly-verified domains.", "flatPath": "v1/customers/{customersId}/userinvitations", "httpMethod": "GET", "id": "cloudidentity.customers.userinvitations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A query string for filtering `UserInvitation` results by their current state, in the format: `\"state=='invited'\"`.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sort order of the list results. You can sort the results in descending order based on either email or last update timestamp but not both, using `order_by=\"email desc\"`. Currently, sorting is supported for `update_time asc`, `update_time desc`, `email asc`, and `email desc`. If not specified, results will be returned based on `email asc` order.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of UserInvitation resources to return. If unspecified, at most 100 resources will be returned. The maximum value is 200; values above 200 will be set to 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListUserInvitations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListBooks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The customer ID of the Google Workspace or Cloud Identity account the UserInvitation resources are associated with.", "location": "path", "pattern": "^customers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/userinvitations", "response": {"$ref": "ListUserInvitationsResponse"}}, "send": {"description": "Sends a UserInvitation to email. If the `UserInvitation` does not exist for this request and it is a valid request, the request creates a `UserInvitation`. **Note:** The `get` and `list` methods have a 48-hour delay where newly-created consumer accounts will not appear in the results. You can still send a `UserInvitation` to those accounts if you know the unmanaged email address and IsInvitableUser==True.", "flatPath": "v1/customers/{customersId}/userinvitations/{userinvitationsId}:send", "httpMethod": "POST", "id": "cloudidentity.customers.userinvitations.send", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `UserInvitation` name in the format `customers/{customer}/userinvitations/{user_email_address}`", "location": "path", "pattern": "^customers/[^/]+/userinvitations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:send", "request": {"$ref": "SendUserInvitationRequest"}, "response": {"$ref": "Operation"}}}}}}, "devices": {"methods": {"cancelWipe": {"description": "Cancels an unfinished device wipe. This operation can be used to cancel device wipe in the gap between the wipe operation returning success and the device being wiped. This operation is possible when the device is in a \"pending wipe\" state. The device enters the \"pending wipe\" state when a wipe device command is issued, but has not yet been sent to the device. The cancel wipe will fail if the wipe command has already been issued to the device.", "flatPath": "v1/devices/{devicesId}:cancelWipe", "httpMethod": "POST", "id": "cloudidentity.devices.cancelWipe", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}`, where device is the unique ID assigned to the Device.", "location": "path", "pattern": "^devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancelWipe", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "create": {"description": "Creates a device. Only company-owned device may be created. **Note**: This method is available only to customers who have one of the following SKUs: Enterprise Standard, Enterprise Plus, Enterprise for Education, and Cloud Identity Premium", "flatPath": "v1/devices", "httpMethod": "POST", "id": "cloudidentity.devices.create", "parameterOrder": [], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}}, "path": "v1/devices", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1Device"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "delete": {"description": "Deletes the specified device.", "flatPath": "v1/devices/{devicesId}", "httpMethod": "DELETE", "id": "cloudidentity.devices.delete", "parameterOrder": ["name"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}`, where device is the unique ID assigned to the Device.", "location": "path", "pattern": "^devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "get": {"description": "Retrieves the specified device.", "flatPath": "v1/devices/{devicesId}", "httpMethod": "GET", "id": "cloudidentity.devices.get", "parameterOrder": ["name"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Customer in the format: `customers/{customer}`, where customer is the customer to whom the device belongs. If you're using this API for your own organization, use `customers/my_customer`. If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in the format: `devices/{device}`, where device is the unique ID assigned to the Device.", "location": "path", "pattern": "^devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1Device"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices", "https://www.googleapis.com/auth/cloud-identity.devices.readonly"]}, "list": {"description": "Lists/Searches devices.", "flatPath": "v1/devices", "httpMethod": "GET", "id": "cloudidentity.devices.list", "parameterOrder": [], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer in the format: `customers/{customer}`, where customer is the customer to whom the device belongs. If you're using this API for your own organization, use `customers/my_customer`. If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "filter": {"description": "Optional. Additional restrictions when fetching list of devices. For a list of search fields, refer to [Mobile device search fields](https://developers.google.com/admin-sdk/directory/v1/search-operators). Multiple search fields are separated by the space character.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Order specification for devices in the response. Only one of the following field names may be used to specify the order: `create_time`, `last_sync_time`, `model`, `os_version`, `device_type` and `serial_number`. `desc` may be specified optionally at the end to specify results to be sorted in descending order. Default order is ascending.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of Devices to return. If unspecified, at most 20 Devices will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDevices` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDevices` must match the call that provided the page token.", "location": "query", "type": "string"}, "view": {"description": "Optional. The view to use for the List request.", "enum": ["VIEW_UNSPECIFIED", "COMPANY_INVENTORY", "USER_ASSIGNED_DEVICES"], "enumDescriptions": ["Default value. The value is unused.", "This view contains all devices imported by the company admin. Each device in the response contains all information specified by the company admin when importing the device (i.e. asset tags). This includes devices that may be unassigned or assigned to users.", "This view contains all devices with at least one user registered on the device. Each device in the response contains all device information, except for asset tags."], "location": "query", "type": "string"}}, "path": "v1/devices", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1ListDevicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices", "https://www.googleapis.com/auth/cloud-identity.devices.readonly"]}, "wipe": {"description": "Wipes all data on the specified device.", "flatPath": "v1/devices/{devicesId}:wipe", "httpMethod": "POST", "id": "cloudidentity.devices.wipe", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:wipe", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1WipeDeviceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}}, "resources": {"deviceUsers": {"methods": {"approve": {"description": "Approves device to access user data.", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}:approve", "httpMethod": "POST", "id": "cloudidentity.devices.deviceUsers.approve", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:approve", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "block": {"description": "Blocks device from accessing user data", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}:block", "httpMethod": "POST", "id": "cloudidentity.devices.deviceUsers.block", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:block", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1BlockDeviceUserRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "cancelWipe": {"description": "Cancels an unfinished user account wipe. This operation can be used to cancel device wipe in the gap between the wipe operation returning success and the device being wiped.", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}:cancelWipe", "httpMethod": "POST", "id": "cloudidentity.devices.deviceUsers.cancelWipe", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancelWipe", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "delete": {"description": "Deletes the specified DeviceUser. This also revokes the user's access to device data.", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}", "httpMethod": "DELETE", "id": "cloudidentity.devices.deviceUsers.delete", "parameterOrder": ["name"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}, "get": {"description": "Retrieves the specified DeviceUser", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}", "httpMethod": "GET", "id": "cloudidentity.devices.deviceUsers.get", "parameterOrder": ["name"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1DeviceUser"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices", "https://www.googleapis.com/auth/cloud-identity.devices.readonly"]}, "list": {"description": "Lists/Searches DeviceUsers.", "flatPath": "v1/devices/{devicesId}/deviceUsers", "httpMethod": "GET", "id": "cloudidentity.devices.deviceUsers.list", "parameterOrder": ["parent"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "filter": {"description": "Optional. Additional restrictions when fetching list of devices. For a list of search fields, refer to [Mobile device search fields](https://developers.google.com/admin-sdk/directory/v1/search-operators). Multiple search fields are separated by the space character.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Order specification for devices in the response.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of DeviceUsers to return. If unspecified, at most 5 DeviceUsers will be returned. The maximum value is 20; values above 20 will be coerced to 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDeviceUsers` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListBooks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. To list all DeviceUsers, set this to \"devices/-\". To list all DeviceUsers owned by a device, set this to the resource name of the device. Format: devices/{device}", "location": "path", "pattern": "^devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/deviceUsers", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1ListDeviceUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices", "https://www.googleapis.com/auth/cloud-identity.devices.readonly"]}, "lookup": {"description": "Looks up resource names of the DeviceUsers associated with the caller's credentials, as well as the properties provided in the request. This method must be called with end-user credentials with the scope: https://www.googleapis.com/auth/cloud-identity.devices.lookup If multiple properties are provided, only DeviceUsers having all of these properties are considered as matches - i.e. the query behaves like an AND. Different platforms require different amounts of information from the caller to ensure that the DeviceUser is uniquely identified. - iOS: No properties need to be passed, the caller's credentials are sufficient to identify the corresponding DeviceUser. - Android: Specifying the 'android_id' field is required. - Desktop: Specifying the 'raw_resource_id' field is required.", "flatPath": "v1/devices/{devicesId}/deviceUsers:lookup", "httpMethod": "GET", "id": "cloudidentity.devices.deviceUsers.lookup", "parameterOrder": ["parent"], "parameters": {"androidId": {"description": "Android Id returned by [Settings.Secure#ANDROID_ID](https://developer.android.com/reference/android/provider/Settings.Secure.html#ANDROID_ID).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of DeviceUsers to return. If unspecified, at most 20 DeviceUsers will be returned. The maximum value is 20; values above 20 will be coerced to 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `LookupDeviceUsers` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `LookupDeviceUsers` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Must be set to \"devices/-/deviceUsers\" to search across all DeviceUser belonging to the user.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers$", "required": true, "type": "string"}, "rawResourceId": {"description": "Raw Resource Id used by Google Endpoint Verification. If the user is enrolled into Google Endpoint Verification, this id will be saved as the 'device_resource_id' field in the following platform dependent files. * macOS: ~/.secureConnect/context_aware_config.json * Windows: %USERPROFILE%\\AppData\\Local\\Google\\Endpoint Verification\\accounts.json * Linux: ~/.secureConnect/context_aware_config.json", "location": "query", "type": "string"}, "userId": {"description": "The user whose DeviceUser's resource name will be fetched. Must be set to 'me' to fetch the DeviceUser's resource name for the calling user.", "location": "query", "type": "string"}}, "path": "v1/{+parent}:lookup", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1LookupSelfDeviceUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices.lookup"]}, "wipe": {"description": "Wipes the user's account on a device. Other data on the device that is not associated with the user's work account is not affected. For example, if a Gmail app is installed on a device that is used for personal and work purposes, and the user is logged in to the Gmail app with their personal account as well as their work account, wiping the \"deviceUser\" by their work administrator will not affect their personal account within Gmail or other apps such as Photos.", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}:wipe", "httpMethod": "POST", "id": "cloudidentity.devices.deviceUsers.wipe", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}/deviceUsers/{device_user}`, where device is the unique ID assigned to the Device, and device_user is the unique ID assigned to the User.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:wipe", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1WipeDeviceUserRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}}, "resources": {"clientStates": {"methods": {"get": {"description": "Gets the client state for the device user", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}/clientStates/{clientStatesId}", "httpMethod": "GET", "id": "cloudidentity.devices.deviceUsers.clientStates.get", "parameterOrder": ["name"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "name": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the ClientState in format: `devices/{device}/deviceUsers/{device_user}/clientStates/{partner}`, where `device` is the unique ID assigned to the Device, `device_user` is the unique ID assigned to the User and `partner` identifies the partner storing the data. To get the client state for devices belonging to your own organization, the `partnerId` is in the format: `customerId-*anystring*`. Where the `customerId` is your organization's customer ID and `anystring` is any suffix. This suffix is used in setting up Custom Access Levels in Context-Aware Access. You may use `my_customer` instead of the customer ID for devices managed by your own organization. You may specify `-` in place of the `{device}`, so the ClientState resource name can be: `devices/-/deviceUsers/{device_user_resource}/clientStates/{partner}`.", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+/clientStates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1ClientState"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices", "https://www.googleapis.com/auth/cloud-identity.devices.readonly"]}, "list": {"description": "Lists the client states for the given search query.", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}/clientStates", "httpMethod": "GET", "id": "cloudidentity.devices.deviceUsers.clientStates.list", "parameterOrder": ["parent"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "filter": {"description": "Optional. Additional restrictions when fetching list of client states.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Order specification for client states in the response.", "location": "query", "type": "string"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListClientStates` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListClientStates` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. To list all ClientStates, set this to \"devices/-/deviceUsers/-\". To list all ClientStates owned by a DeviceUser, set this to the resource name of the DeviceUser. Format: devices/{device}/deviceUsers/{deviceUser}", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/clientStates", "response": {"$ref": "GoogleAppsCloudidentityDevicesV1ListClientStatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices", "https://www.googleapis.com/auth/cloud-identity.devices.readonly"]}, "patch": {"description": "Updates the client state for the device user **Note**: This method is available only to customers who have one of the following SKUs: Enterprise Standard, Enterprise Plus, Enterprise for Education, and Cloud Identity Premium", "flatPath": "v1/devices/{devicesId}/deviceUsers/{deviceUsersId}/clientStates/{clientStatesId}", "httpMethod": "PATCH", "id": "cloudidentity.devices.deviceUsers.clientStates.patch", "parameterOrder": ["name"], "parameters": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "location": "query", "type": "string"}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the ClientState in format: `devices/{device}/deviceUsers/{device_user}/clientState/{partner}`, where partner corresponds to the partner storing the data. For partners belonging to the \"BeyondCorp Alliance\", this is the partner ID specified to you by Google. For all other callers, this is a string of the form: `{customer}-suffix`, where `customer` is your customer ID. The *suffix* is any string the caller specifies. This string will be displayed verbatim in the administration console. This suffix is used in setting up Custom Access Levels in Context-Aware Access. Your organization's customer ID can be obtained from the URL: `GET https://www.googleapis.com/admin/directory/v1/customers/my_customer` The `id` field in the response contains the customer ID starting with the letter 'C'. The customer ID to be used in this API is the string after the letter 'C' (not including 'C')", "location": "path", "pattern": "^devices/[^/]+/deviceUsers/[^/]+/clientStates/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Comma-separated list of fully qualified names of fields to be updated. If not specified, all updatable fields in ClientState are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleAppsCloudidentityDevicesV1ClientState"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.devices"]}}}}}}}, "groups": {"methods": {"create": {"description": "Creates a Group.", "flatPath": "v1/groups", "httpMethod": "POST", "id": "cloudidentity.groups.create", "parameterOrder": [], "parameters": {"initialGroupConfig": {"description": "Optional. The initial configuration option for the `Group`.", "enum": ["INITIAL_GROUP_CONFIG_UNSPECIFIED", "WITH_INITIAL_OWNER", "EMPTY"], "enumDescriptions": ["Default. Should not be used.", "The end user making the request will be added as the initial owner of the `Group`.", "An empty group is created without any initial owners. This can only be used by admins of the domain."], "location": "query", "type": "string"}}, "path": "v1/groups", "request": {"$ref": "Group"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `Group`.", "flatPath": "v1/groups/{groupsId}", "httpMethod": "DELETE", "id": "cloudidentity.groups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Group` to retrieve. Must be of the form `groups/{group}`.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `Group`.", "flatPath": "v1/groups/{groupsId}", "httpMethod": "GET", "id": "cloudidentity.groups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Group` to retrieve. Must be of the form `groups/{group}`.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Group"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "getSecuritySettings": {"description": "Get Security Settings", "flatPath": "v1/groups/{groupsId}/securitySettings", "httpMethod": "GET", "id": "cloudidentity.groups.getSecuritySettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The security settings to retrieve. Format: `groups/{group_id}/securitySettings`", "location": "path", "pattern": "^groups/[^/]+/securitySettings$", "required": true, "type": "string"}, "readMask": {"description": "Field-level read mask of which fields to return. \"*\" returns all fields. If not specified, all fields will be returned. May only contain the following field: `member_restriction`.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "SecuritySettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the `Group` resources under a customer or namespace.", "flatPath": "v1/groups", "httpMethod": "GET", "id": "cloudidentity.groups.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of results to return. Note that the number of results returned may be less than this value even if there are more available results. To fetch all results, clients must continue calling this method repeatedly until the response no longer contains a `next_page_token`. If unspecified, defaults to 200 for `View.BASIC` and to 50 for `View.FULL`. Must not be greater than 1000 for `View.BASIC` or 500 for `View.FULL`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous list request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource under which to list all `Group` resources. Must be of the form `identitysources/{identity_source}` for external- identity-mapped groups or `customers/{customer_id}` for Google Groups. The `customer_id` must begin with \"C\" (for example, 'C046psxkn'). [Find your customer ID.] (https://support.google.com/cloudidentity/answer/10070793)", "location": "query", "type": "string"}, "view": {"description": "The level of detail to be returned. If unspecified, defaults to `View.BASIC`.", "enum": ["VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["Default. Should not be used.", "Only basic resource information is returned.", "All resource information is returned."], "location": "query", "type": "string"}}, "path": "v1/groups", "response": {"$ref": "ListGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "lookup": {"description": "Looks up the [resource name](https://cloud.google.com/apis/design/resource_names) of a `Group` by its `EntityKey`.", "flatPath": "v1/groups:lookup", "httpMethod": "GET", "id": "cloudidentity.groups.lookup", "parameterOrder": [], "parameters": {"groupKey.id": {"description": "The ID of the entity. For Google-managed entities, the `id` should be the email address of an existing group or user. Email addresses need to adhere to [name guidelines for users and groups](https://support.google.com/a/answer/9193374). For external-identity-mapped entities, the `id` must be a string conforming to the Identity Source's requirements. Must be unique within a `namespace`.", "location": "query", "type": "string"}, "groupKey.namespace": {"description": "The namespace in which the entity exists. If not specified, the `Entity<PERSON><PERSON>` represents a Google-managed entity such as a Google user or a Google Group. If specified, the `Entity<PERSON>ey` represents an external-identity-mapped group. The namespace must correspond to an identity source created in Admin Console and must be in the form of `identitysources/{identity_source}`.", "location": "query", "type": "string"}}, "path": "v1/groups:lookup", "response": {"$ref": "LookupGroupNameResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a `Group`.", "flatPath": "v1/groups/{groupsId}", "httpMethod": "PATCH", "id": "cloudidentity.groups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Group`. Shall be of the form `groups/{group}`.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The names of fields to update. May only contain the following field names: `display_name`, `description`, `labels`.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Group"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Searches for `Group` resources matching a specified query.", "flatPath": "v1/groups:search", "httpMethod": "GET", "id": "cloudidentity.groups.search", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of results to return. Note that the number of results returned may be less than this value even if there are more available results. To fetch all results, clients must continue calling this method repeatedly until the response no longer contains a `next_page_token`. If unspecified, defaults to 200 for `GroupView.BASIC` and 50 for `GroupView.FULL`. Must not be greater than 1000 for `GroupView.BASIC` or 500 for `GroupView.FULL`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous search request, if any.", "location": "query", "type": "string"}, "query": {"description": "Required. The search query. * Must be specified in [Common Expression Language](https://opensource.google/projects/cel). * Must contain equality operators on the parent, e.g. `parent == 'customers/{customer_id}'`. The `customer_id` must begin with \"C\" (for example, 'C046psxkn'). [Find your customer ID.] (https://support.google.com/cloudidentity/answer/10070793) * Can contain optional inclusion operators on `labels` such as `'cloudidentity.googleapis.com/groups.discussion_forum' in labels`). * Can contain an optional equality operator on `domain_name`. e.g. `domain_name == 'examplepetstore.com'` * Can contain optional `startsWith/contains/equality` operators on `group_key`, e.g. `group_key.startsWith('dev')`, `group_key.contains('dev'), group_key == '<EMAIL>'` * Can contain optional `startsWith/contains/equality` operators on `display_name`, such as `display_name.startsWith('dev')` , `display_name.contains('dev')`, `display_name == 'dev'`", "location": "query", "type": "string"}, "view": {"description": "The level of detail to be returned. If unspecified, defaults to `View.BASIC`.", "enum": ["VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["Default. Should not be used.", "Only basic resource information is returned.", "All resource information is returned."], "location": "query", "type": "string"}}, "path": "v1/groups:search", "response": {"$ref": "SearchGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "updateSecuritySettings": {"description": "Update Security Settings", "flatPath": "v1/groups/{groupsId}/securitySettings", "httpMethod": "PATCH", "id": "cloudidentity.groups.updateSecuritySettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the security settings. Shall be of the form `groups/{group_id}/securitySettings`.", "location": "path", "pattern": "^groups/[^/]+/securitySettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fully-qualified names of fields to update. May only contain the following field: `member_restriction.query`.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "SecuritySettings"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"memberships": {"methods": {"checkTransitiveMembership": {"description": "Check a potential member for membership in a group. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. If the account of the member is not one of these, a 403 (PERMISSION_DENIED) HTTP status code will be returned. A member has membership to a group as long as there is a single viewable transitive membership between the group and the member. The actor must have view permissions to at least one transitive membership between the member and group.", "flatPath": "v1/groups/{groupsId}/memberships:checkTransitiveMembership", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.checkTransitiveMembership", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "[Resource name](https://cloud.google.com/apis/design/resource_names) of the group to check the transitive membership in. Format: `groups/{group}`, where `group` is the unique id assigned to the Group to which the Membership belongs to.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Required. A CEL expression that MUST include member specification. This is a `required` field. Certain groups are uniquely identified by both a 'member_key_id' and a 'member_key_namespace', which requires an additional query input: 'member_key_namespace'. Example query: `member_key_id == 'member_key_id_value'`", "location": "query", "type": "string"}}, "path": "v1/{+parent}/memberships:checkTransitiveMembership", "response": {"$ref": "CheckTransitiveMembershipResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a `Membership`.", "flatPath": "v1/groups/{groupsId}/memberships", "httpMethod": "POST", "id": "cloudidentity.groups.memberships.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent `Group` resource under which to create the `Membership`. Must be of the form `groups/{group}`.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/memberships", "request": {"$ref": "Membership"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a `Membership`.", "flatPath": "v1/groups/{groupsId}/memberships/{membershipsId}", "httpMethod": "DELETE", "id": "cloudidentity.groups.memberships.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Membership` to delete. Must be of the form `groups/{group}/memberships/{membership}`", "location": "path", "pattern": "^groups/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves a `Membership`.", "flatPath": "v1/groups/{groupsId}/memberships/{membershipsId}", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Membership` to retrieve. Must be of the form `groups/{group}/memberships/{membership}`.", "location": "path", "pattern": "^groups/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Membership"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "getMembershipGraph": {"description": "Get a membership graph of just a member or both a member and a group. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. If the account of the member is not one of these, a 403 (PERMISSION_DENIED) HTTP status code will be returned. Given a member, the response will contain all membership paths from the member. Given both a group and a member, the response will contain all membership paths between the group and the member.", "flatPath": "v1/groups/{groupsId}/memberships:getMembershipGraph", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.getMembershipGraph", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. [Resource name](https://cloud.google.com/apis/design/resource_names) of the group to search transitive memberships in. Format: `groups/{group}`, where `group` is the unique ID assigned to the Group to which the Membership belongs to. group can be a wildcard collection id \"-\". When a group is specified, the membership graph will be constrained to paths between the member (defined in the query) and the parent. If a wildcard collection is provided, all membership paths connected to the member will be returned.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Required. A CEL expression that MUST include member specification AND label(s). Certain groups are uniquely identified by both a 'member_key_id' and a 'member_key_namespace', which requires an additional query input: 'member_key_namespace'. Example query: `member_key_id == 'member_key_id_value' && in labels`", "location": "query", "type": "string"}}, "path": "v1/{+parent}/memberships:getMembershipGraph", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the `Membership`s within a `Group`.", "flatPath": "v1/groups/{groupsId}/memberships", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of results to return. Note that the number of results returned may be less than this value even if there are more available results. To fetch all results, clients must continue calling this method repeatedly until the response no longer contains a `next_page_token`. If unspecified, defaults to 200 for `GroupView.BASIC` and to 50 for `GroupView.FULL`. Must not be greater than 1000 for `GroupView.BASIC` or 500 for `GroupView.FULL`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous search request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent `Group` resource under which to lookup the `Membership` name. Must be of the form `groups/{group}`.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of detail to be returned. If unspecified, defaults to `View.BASIC`.", "enum": ["VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["Default. Should not be used.", "Only basic resource information is returned.", "All resource information is returned."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/memberships", "response": {"$ref": "ListMembershipsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "lookup": {"description": "Looks up the [resource name](https://cloud.google.com/apis/design/resource_names) of a `Membership` by its `EntityKey`.", "flatPath": "v1/groups/{groupsId}/memberships:lookup", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.lookup", "parameterOrder": ["parent"], "parameters": {"memberKey.id": {"description": "The ID of the entity. For Google-managed entities, the `id` should be the email address of an existing group or user. Email addresses need to adhere to [name guidelines for users and groups](https://support.google.com/a/answer/9193374). For external-identity-mapped entities, the `id` must be a string conforming to the Identity Source's requirements. Must be unique within a `namespace`.", "location": "query", "type": "string"}, "memberKey.namespace": {"description": "The namespace in which the entity exists. If not specified, the `Entity<PERSON><PERSON>` represents a Google-managed entity such as a Google user or a Google Group. If specified, the `Entity<PERSON>ey` represents an external-identity-mapped group. The namespace must correspond to an identity source created in Admin Console and must be in the form of `identitysources/{identity_source}`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent `Group` resource under which to lookup the `Membership` name. Must be of the form `groups/{group}`.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/memberships:lookup", "response": {"$ref": "LookupMembershipNameResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "modifyMembershipRoles": {"description": "Modifies the `MembershipRole`s of a `Membership`.", "flatPath": "v1/groups/{groupsId}/memberships/{membershipsId}:modifyMembershipRoles", "httpMethod": "POST", "id": "cloudidentity.groups.memberships.modifyMembershipRoles", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Membership` whose roles are to be modified. Must be of the form `groups/{group}/memberships/{membership}`.", "location": "path", "pattern": "^groups/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:modifyMembershipRoles", "request": {"$ref": "ModifyMembershipRolesRequest"}, "response": {"$ref": "ModifyMembershipRolesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-platform"]}, "searchDirectGroups": {"description": "Searches direct groups of a member.", "flatPath": "v1/groups/{groupsId}/memberships:searchDirectGroups", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.searchDirectGroups", "parameterOrder": ["parent"], "parameters": {"orderBy": {"description": "The ordering of membership relation for the display name or email in the response. The syntax for this field can be found at https://cloud.google.com/apis/design/design_patterns#sorting_order. Example: Sort by the ascending display name: order_by=\"group_name\" or order_by=\"group_name asc\". Sort by the descending display name: order_by=\"group_name desc\". Sort by the ascending group key: order_by=\"group_key\" or order_by=\"group_key asc\". Sort by the descending group key: order_by=\"group_key desc\".", "location": "query", "type": "string"}, "pageSize": {"description": "The default page size is 200 (max 1000).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous list request, if any", "location": "query", "type": "string"}, "parent": {"description": "[Resource name](https://cloud.google.com/apis/design/resource_names) of the group to search transitive memberships in. Format: groups/{group_id}, where group_id is always '-' as this API will search across all groups for a given member.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Required. A CEL expression that MUST include member specification AND label(s). Users can search on label attributes of groups. CONTAINS match ('in') is supported on labels. Identity-mapped groups are uniquely identified by both a `member_key_id` and a `member_key_namespace`, which requires an additional query input: `member_key_namespace`. Example query: `member_key_id == 'member_key_id_value' && 'label_value' in labels`", "location": "query", "type": "string"}}, "path": "v1/{+parent}/memberships:searchDirectGroups", "response": {"$ref": "SearchDirectGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "searchTransitiveGroups": {"description": "Search transitive groups of a member. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. If the account of the member is not one of these, a 403 (PERMISSION_DENIED) HTTP status code will be returned. A transitive group is any group that has a direct or indirect membership to the member. Actor must have view permissions all transitive groups.", "flatPath": "v1/groups/{groupsId}/memberships:searchTransitiveGroups", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.searchTransitiveGroups", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The default page size is 200 (max 1000).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous list request, if any.", "location": "query", "type": "string"}, "parent": {"description": "[Resource name](https://cloud.google.com/apis/design/resource_names) of the group to search transitive memberships in. Format: `groups/{group}`, where `group` is always '-' as this API will search across all groups for a given member.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Required. A CEL expression that MUST include member specification AND label(s). This is a `required` field. Users can search on label attributes of groups. CONTAINS match ('in') is supported on labels. Identity-mapped groups are uniquely identified by both a `member_key_id` and a `member_key_namespace`, which requires an additional query input: `member_key_namespace`. Example query: `member_key_id == 'member_key_id_value' && in labels` Query may optionally contain equality operators on the parent of the group restricting the search within a particular customer, e.g. `parent == 'customers/{customer_id}'`. The `customer_id` must begin with \"C\" (for example, 'C046psxkn'). This filtering is only supported for Admins with groups read permissions on the input customer. Example query: `member_key_id == 'member_key_id_value' && in labels && parent == 'customers/C046psxkn'`", "location": "query", "type": "string"}}, "path": "v1/{+parent}/memberships:searchTransitiveGroups", "response": {"$ref": "SearchTransitiveGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "searchTransitiveMemberships": {"description": "Search transitive memberships of a group. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. If the account of the group is not one of these, a 403 (PERMISSION_DENIED) HTTP status code will be returned. A transitive membership is any direct or indirect membership of a group. Actor must have view permissions to all transitive memberships.", "flatPath": "v1/groups/{groupsId}/memberships:searchTransitiveMemberships", "httpMethod": "GET", "id": "cloudidentity.groups.memberships.searchTransitiveMemberships", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The default page size is 200 (max 1000).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous list request, if any.", "location": "query", "type": "string"}, "parent": {"description": "[Resource name](https://cloud.google.com/apis/design/resource_names) of the group to search transitive memberships in. Format: `groups/{group}`, where `group` is the unique ID assigned to the Group.", "location": "path", "pattern": "^groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/memberships:searchTransitiveMemberships", "response": {"$ref": "SearchTransitiveMembershipsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.groups", "https://www.googleapis.com/auth/cloud-identity.groups.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}}, "inboundSamlSsoProfiles": {"methods": {"create": {"description": "Creates an InboundSamlSsoProfile for a customer. When the target customer has enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448), the `Operation` in the response will have `\"done\": false`, it will not have a response, and the metadata will have `\"state\": \"awaiting-multi-party-approval\"`.", "flatPath": "v1/inboundSamlSsoProfiles", "httpMethod": "POST", "id": "cloudidentity.inboundSamlSsoProfiles.create", "parameterOrder": [], "parameters": {}, "path": "v1/inboundSamlSsoProfiles", "request": {"$ref": "InboundSamlSsoProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an InboundSamlSsoProfile.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}", "httpMethod": "DELETE", "id": "cloudidentity.inboundSamlSsoProfiles.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the InboundSamlSsoProfile to delete. Format: `inboundSamlSsoProfiles/{sso_profile_id}`", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an InboundSamlSsoProfile.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}", "httpMethod": "GET", "id": "cloudidentity.inboundSamlSsoProfiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the InboundSamlSsoProfile to get. Format: `inboundSamlSsoProfiles/{sso_profile_id}`", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InboundSamlSsoProfile"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists InboundSamlSsoProfiles for a customer.", "flatPath": "v1/inboundSamlSsoProfiles", "httpMethod": "GET", "id": "cloudidentity.inboundSamlSsoProfiles.list", "parameterOrder": [], "parameters": {"filter": {"description": "A [Common Expression Language](https://github.com/google/cel-spec) expression to filter the results. The only supported filter is filtering by customer. For example: `customer==\"customers/C0123abc\"`. Omitting the filter or specifying a filter of `customer==\"customers/my_customer\"` will return the profiles for the customer that the caller (authenticated user) belongs to.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of InboundSamlSsoProfiles to return. The service may return fewer than this value. If omitted (or defaulted to zero) the server will use a sensible default. This default may change over time. The maximum allowed value is 100. Requests with page_size greater than that will be silently interpreted as having this maximum value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListInboundSamlSsoProfiles` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInboundSamlSsoProfiles` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1/inboundSamlSsoProfiles", "response": {"$ref": "ListInboundSamlSsoProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an InboundSamlSsoProfile. When the target customer has enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448), the `Operation` in the response will have `\"done\": false`, it will not have a response, and the metadata will have `\"state\": \"awaiting-multi-party-approval\"`.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}", "httpMethod": "PATCH", "id": "cloudidentity.inboundSamlSsoProfiles.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the SAML SSO profile.", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InboundSamlSsoProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"idpCredentials": {"methods": {"add": {"description": "Adds an IdpCredential. Up to 2 credentials are allowed. When the target customer has enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448), the `Operation` in the response will have `\"done\": false`, it will not have a response, and the metadata will have `\"state\": \"awaiting-multi-party-approval\"`.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials:add", "httpMethod": "POST", "id": "cloudidentity.inboundSamlSsoProfiles.idpCredentials.add", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The InboundSamlSsoProfile that owns the IdpCredential. Format: `inboundSamlSsoProfiles/{sso_profile_id}`", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/idpCredentials:add", "request": {"$ref": "AddIdpCredentialRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an IdpCredential.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials/{idpCredentialsId}", "httpMethod": "DELETE", "id": "cloudidentity.inboundSamlSsoProfiles.idpCredentials.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the IdpCredential to delete. Format: `inboundSamlSsoProfiles/{sso_profile_id}/idpCredentials/{idp_credential_id}`", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+/idpCredentials/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an IdpCredential.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials/{idpCredentialsId}", "httpMethod": "GET", "id": "cloudidentity.inboundSamlSsoProfiles.idpCredentials.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the IdpCredential to retrieve. Format: `inboundSamlSsoProfiles/{sso_profile_id}/idpCredentials/{idp_credential_id}`", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+/idpCredentials/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "IdpCredential"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns a list of IdpCredentials in an InboundSamlSsoProfile.", "flatPath": "v1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials", "httpMethod": "GET", "id": "cloudidentity.inboundSamlSsoProfiles.idpCredentials.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of `IdpCredential`s to return. The service may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListIdpCredentials` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListIdpCredentials` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of `IdpCredential`s. Format: `inboundSamlSsoProfiles/{sso_profile_id}`", "location": "path", "pattern": "^inboundSamlSsoProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/idpCredentials", "response": {"$ref": "ListIdpCredentialsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly", "https://www.googleapis.com/auth/cloud-platform"]}}}}}, "inboundSsoAssignments": {"methods": {"create": {"description": "Creates an InboundSsoAssignment for users and devices in a `Customer` under a given `Group` or `OrgUnit`.", "flatPath": "v1/inboundSsoAssignments", "httpMethod": "POST", "id": "cloudidentity.inboundSsoAssignments.create", "parameterOrder": [], "parameters": {}, "path": "v1/inboundSsoAssignments", "request": {"$ref": "InboundSsoAssignment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an InboundSsoAssignment. To disable SSO, <PERSON>reate (or Update) an assignment that has `sso_mode` == `SSO_OFF`.", "flatPath": "v1/inboundSsoAssignments/{inboundSsoAssignmentsId}", "httpMethod": "DELETE", "id": "cloudidentity.inboundSsoAssignments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the InboundSsoAssignment to delete. Format: `inboundSsoAssignments/{assignment}`", "location": "path", "pattern": "^inboundSsoAssignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an InboundSsoAssignment.", "flatPath": "v1/inboundSsoAssignments/{inboundSsoAssignmentsId}", "httpMethod": "GET", "id": "cloudidentity.inboundSsoAssignments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The [resource name](https://cloud.google.com/apis/design/resource_names) of the InboundSsoAssignment to fetch. Format: `inboundSsoAssignments/{assignment}`", "location": "path", "pattern": "^inboundSsoAssignments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InboundSsoAssignment"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the InboundSsoAssignments for a `Customer`.", "flatPath": "v1/inboundSsoAssignments", "httpMethod": "GET", "id": "cloudidentity.inboundSsoAssignments.list", "parameterOrder": [], "parameters": {"filter": {"description": "A CEL expression to filter the results. The only supported filter is filtering by customer. For example: `customer==customers/C0123abc`. Omitting the filter or specifying a filter of `customer==customers/my_customer` will return the assignments for the customer that the caller (authenticated user) belongs to.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of assignments to return. The service may return fewer than this value. If omitted (or defaulted to zero) the server will use a sensible default. This default may change over time. The maximum allowed value is 100, though requests with page_size greater than that will be silently interpreted as having this maximum value. This may increase in the futue.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListInboundSsoAssignments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInboundSsoAssignments` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1/inboundSsoAssignments", "response": {"$ref": "ListInboundSsoAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an InboundSsoAssignment. The body of this request is the `inbound_sso_assignment` field and the `update_mask` is relative to that. For example: a PATCH to `/v1/inboundSsoAssignments/0abcdefg1234567&update_mask=rank` with a body of `{ \"rank\": 1 }` moves that (presumably group-targeted) SSO assignment to the highest priority and shifts any other group-targeted assignments down in priority.", "flatPath": "v1/inboundSsoAssignments/{inboundSsoAssignmentsId}", "httpMethod": "PATCH", "id": "cloudidentity.inboundSsoAssignments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Inbound SSO Assignment.", "location": "path", "pattern": "^inboundSsoAssignments/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InboundSsoAssignment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.inboundsso", "https://www.googleapis.com/auth/cloud-platform"]}}}, "policies": {"methods": {"get": {"description": "Get a Policy", "flatPath": "v1/policies/{policiesId}", "httpMethod": "GET", "id": "cloudidentity.policies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the policy to retrieve. Format: \"policies/{policy}\".", "location": "path", "pattern": "^policies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.policies", "https://www.googleapis.com/auth/cloud-identity.policies.readonly"]}, "list": {"description": "List Policies", "flatPath": "v1/policies", "httpMethod": "GET", "id": "cloudidentity.policies.list", "parameterOrder": [], "parameters": {"filter": {"description": "Optional. A CEL expression for filtering the results. Policies can be filtered by application with this expression: setting.type.matches('^settings/gmail\\\\..*$') Policies can be filtered by setting type with this expression: setting.type.matches('^.*\\\\.service_status$') A maximum of one of the above setting.type clauses can be used. Policies can be filtered by customer with this expression: customer == \"customers/{customer}\" Where `customer` is the `id` from the [Admin SDK `Customer` resource](https://developers.google.com/admin-sdk/directory/reference/rest/v1/customers). You may use `customers/my_customer` to specify your own organization. When no customer is mentioned it will be default to customers/my_customer. A maximum of one customer clause can be used. The above clauses can only be combined together in a single filter expression with the `&&` operator.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return. The service can return fewer than this number. If omitted or set to 0, the default is 50 results per page. The maximum allowed value is 100. `page_size` values greater than 100 default to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The pagination token received from a prior call to PoliciesService.ListPolicies to retrieve the next page of results. When paginating, all other parameters provided to `ListPoliciesRequest` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1/policies", "response": {"$ref": "ListPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-identity.policies", "https://www.googleapis.com/auth/cloud-identity.policies.readonly"]}}}}, "revision": "20250701", "rootUrl": "https://cloudidentity.googleapis.com/", "schemas": {"AddIdpCredentialOperationMetadata": {"description": "LRO response metadata for InboundSamlSsoProfilesService.AddIdpCredential.", "id": "AddIdpCredentialOperationMetadata", "properties": {"state": {"description": "State of this Operation Will be \"awaiting-multi-party-approval\" when the operation is deferred due to the target customer having enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448).", "type": "string"}}, "type": "object"}, "AddIdpCredentialRequest": {"description": "The request for creating an IdpCredential with its associated payload. An InboundSamlSsoProfile can own up to 2 credentials.", "id": "AddIdpCredentialRequest", "properties": {"pemData": {"description": "PEM encoded x509 certificate containing the public key for verifying IdP signatures.", "type": "string"}}, "type": "object"}, "CancelUserInvitationRequest": {"description": "Request to cancel sent invitation for target email in UserInvitation.", "id": "CancelUserInvitationRequest", "properties": {}, "type": "object"}, "CheckTransitiveMembershipResponse": {"description": "The response message for MembershipsService.CheckTransitiveMembership.", "id": "CheckTransitiveMembershipResponse", "properties": {"hasMembership": {"description": "Response does not include the possible roles of a member since the behavior of this rpc is not all-or-nothing unlike the other rpcs. So, it may not be possible to list all the roles definitively, due to possible lack of authorization in some of the paths.", "type": "boolean"}}, "type": "object"}, "CreateGroupMetadata": {"description": "Metadata for CreateGroup LRO.", "id": "CreateGroupMetadata", "properties": {}, "type": "object"}, "CreateInboundSamlSsoProfileOperationMetadata": {"description": "LRO response metadata for InboundSamlSsoProfilesService.CreateInboundSamlSsoProfile.", "id": "CreateInboundSamlSsoProfileOperationMetadata", "properties": {"state": {"description": "State of this Operation Will be \"awaiting-multi-party-approval\" when the operation is deferred due to the target customer having enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448).", "type": "string"}}, "type": "object"}, "CreateInboundSsoAssignmentOperationMetadata": {"description": "LRO response metadata for InboundSsoAssignmentsService.CreateInboundSsoAssignment.", "id": "CreateInboundSsoAssignmentOperationMetadata", "properties": {}, "type": "object"}, "CreateMembershipMetadata": {"description": "Metadata for CreateMembership LRO.", "id": "CreateMembershipMetadata", "properties": {}, "type": "object"}, "DeleteGroupMetadata": {"description": "Metadata for DeleteGroup LRO.", "id": "DeleteGroupMetadata", "properties": {}, "type": "object"}, "DeleteIdpCredentialOperationMetadata": {"description": "LRO response metadata for InboundSamlSsoProfilesService.DeleteIdpCredential.", "id": "DeleteIdpCredentialOperationMetadata", "properties": {}, "type": "object"}, "DeleteInboundSamlSsoProfileOperationMetadata": {"description": "LRO response metadata for InboundSamlSsoProfilesService.DeleteInboundSamlSsoProfile.", "id": "DeleteInboundSamlSsoProfileOperationMetadata", "properties": {}, "type": "object"}, "DeleteInboundSsoAssignmentOperationMetadata": {"description": "LRO response metadata for InboundSsoAssignmentsService.DeleteInboundSsoAssignment.", "id": "DeleteInboundSsoAssignmentOperationMetadata", "properties": {}, "type": "object"}, "DeleteMembershipMetadata": {"description": "Metadata for DeleteMembership LRO.", "id": "DeleteMembershipMetadata", "properties": {}, "type": "object"}, "DsaPublicKeyInfo": {"description": "Information of a DSA public key.", "id": "DsaPublicKeyInfo", "properties": {"keySize": {"description": "Key size in bits (size of parameter P).", "format": "int32", "type": "integer"}}, "type": "object"}, "DynamicGroupMetadata": {"description": "Dynamic group metadata like queries and status.", "id": "DynamicGroupMetadata", "properties": {"queries": {"description": "Memberships will be the union of all queries. Only one entry with USER resource is currently supported. Customers can create up to 500 dynamic groups.", "items": {"$ref": "DynamicGroupQuery"}, "type": "array"}, "status": {"$ref": "DynamicGroupStatus", "description": "Output only. Status of the dynamic group.", "readOnly": true}}, "type": "object"}, "DynamicGroupQuery": {"description": "Defines a query on a resource.", "id": "DynamicGroupQuery", "properties": {"query": {"description": "Query that determines the memberships of the dynamic group. Examples: All users with at least one `organizations.department` of engineering. `user.organizations.exists(org, org.department=='engineering')` All users with at least one location that has `area` of `foo` and `building_id` of `bar`. `user.locations.exists(loc, loc.area=='foo' && loc.building_id=='bar')` All users with any variation of the name <PERSON> (case-insensitive queries add `equalsIgnoreCase()` to the value being queried). `user.name.value.equalsIgnoreCase('jOhn DoE')`", "type": "string"}, "resourceType": {"description": "Resource type for the Dynamic Group Query", "enum": ["RESOURCE_TYPE_UNSPECIFIED", "USER"], "enumDescriptions": ["Default value (not valid)", "For queries on User"], "type": "string"}}, "type": "object"}, "DynamicGroupStatus": {"description": "The current status of a dynamic group along with timestamp.", "id": "DynamicGroupStatus", "properties": {"status": {"description": "Status of the dynamic group.", "enum": ["STATUS_UNSPECIFIED", "UP_TO_DATE", "UPDATING_MEMBERSHIPS", "INVALID_QUERY"], "enumDescriptions": ["De<PERSON><PERSON>.", "The dynamic group is up-to-date.", "The dynamic group has just been created and memberships are being updated.", "Group is in an unrecoverable state and its memberships can't be updated."], "type": "string"}, "statusTime": {"description": "The latest time at which the dynamic group is guaranteed to be in the given status. If status is `UP_TO_DATE`, the latest time at which the dynamic group was confirmed to be up-to-date. If status is `UPDATING_MEMBERSHIPS`, the time at which dynamic group was created.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "EntityKey": {"description": "A unique identifier for an entity in the Cloud Identity Groups API. An entity can represent either a group with an optional `namespace` or a user without a `namespace`. The combination of `id` and `namespace` must be unique; however, the same `id` can be used with different `namespace`s.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"id": {"description": "The ID of the entity. For Google-managed entities, the `id` should be the email address of an existing group or user. Email addresses need to adhere to [name guidelines for users and groups](https://support.google.com/a/answer/9193374). For external-identity-mapped entities, the `id` must be a string conforming to the Identity Source's requirements. Must be unique within a `namespace`.", "type": "string"}, "namespace": {"description": "The namespace in which the entity exists. If not specified, the `Entity<PERSON><PERSON>` represents a Google-managed entity such as a Google user or a Google Group. If specified, the `Entity<PERSON>ey` represents an external-identity-mapped group. The namespace must correspond to an identity source created in Admin Console and must be in the form of `identitysources/{identity_source}`.", "type": "string"}}, "type": "object"}, "ExpiryDetail": {"description": "The `MembershipRole` expiry details.", "id": "ExpiryDetail", "properties": {"expireTime": {"description": "The time at which the `MembershipRole` will expire.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GetMembershipGraphMetadata": {"description": "Metadata of GetMembershipGraphResponse LRO. This is currently empty to permit future extensibility.", "id": "GetMembershipGraphMetadata", "properties": {}, "type": "object"}, "GetMembershipGraphResponse": {"description": "The response message for MembershipsService.GetMembershipGraph.", "id": "GetMembershipGraphResponse", "properties": {"adjacencyList": {"description": "The membership graph's path information represented as an adjacency list.", "items": {"$ref": "MembershipAdjacencyList"}, "type": "array"}, "groups": {"description": "The resources representing each group in the adjacency list. Each group in this list can be correlated to a 'group' of the MembershipAdjacencyList using the 'name' of the Group resource.", "items": {"$ref": "Group"}, "type": "array"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1AndroidAttributes": {"description": "Resource representing the Android specific attributes of a Device.", "id": "GoogleAppsCloudidentityDevicesV1AndroidAttributes", "properties": {"ctsProfileMatch": {"description": "Whether the device passes Android CTS compliance.", "type": "boolean"}, "enabledUnknownSources": {"description": "Whether applications from unknown sources can be installed on device.", "type": "boolean"}, "hasPotentiallyHarmfulApps": {"description": "Whether any potentially harmful apps were detected on the device.", "type": "boolean"}, "ownerProfileAccount": {"description": "Whether this account is on an owner/primary profile. For phones, only true for owner profiles. Android 4+ devices can have secondary or restricted user profiles.", "type": "boolean"}, "ownershipPrivilege": {"description": "Ownership privileges on device.", "enum": ["OWNERSHIP_PRIVILEGE_UNSPECIFIED", "DEVICE_ADMINISTRATOR", "PROFILE_OWNER", "DEVICE_OWNER"], "enumDescriptions": ["Ownership privilege is not set.", "Active device administrator privileges on the device.", "Profile Owner privileges. The account is in a managed corporate profile.", "Device Owner privileges on the device."], "type": "string"}, "supportsWorkProfile": {"description": "Whether device supports Android work profiles. If false, this service will not block access to corp data even if an administrator turns on the \"Enforce Work Profile\" policy.", "type": "boolean"}, "verifiedBoot": {"description": "Whether Android verified boot status is GREEN.", "type": "boolean"}, "verifyAppsEnabled": {"description": "Whether Google Play Protect Verify Apps is enabled.", "type": "boolean"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserMetadata": {"description": "Metadata for ApproveDeviceUser LRO.", "id": "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserRequest": {"description": "Request message for approving the device to access user data.", "id": "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserRequest", "properties": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserResponse": {"description": "Response message for approving the device to access user data.", "id": "GoogleAppsCloudidentityDevicesV1ApproveDeviceUserResponse", "properties": {"deviceUser": {"$ref": "GoogleAppsCloudidentityDevicesV1DeviceUser", "description": "Resultant DeviceUser object for the action."}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1BlockDeviceUserMetadata": {"description": "Metadata for BlockDeviceUser LRO.", "id": "GoogleAppsCloudidentityDevicesV1BlockDeviceUserMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1BlockDeviceUserRequest": {"description": "Request message for blocking account on device.", "id": "GoogleAppsCloudidentityDevicesV1BlockDeviceUserRequest", "properties": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1BlockDeviceUserResponse": {"description": "Response message for blocking the device from accessing user data.", "id": "GoogleAppsCloudidentityDevicesV1BlockDeviceUserResponse", "properties": {"deviceUser": {"$ref": "GoogleAppsCloudidentityDevicesV1DeviceUser", "description": "Resultant DeviceUser object for the action."}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1BrowserAttributes": {"description": "Contains information about browser profiles reported by the [Endpoint Verification extension](https://chromewebstore.google.com/detail/endpoint-verification/callobklhcbilhphinckomhgkigmfocg?pli=1).", "id": "GoogleAppsCloudidentityDevicesV1BrowserAttributes", "properties": {"chromeBrowserInfo": {"$ref": "GoogleAppsCloudidentityDevicesV1BrowserInfo", "description": "Represents the current state of the [Chrome browser attributes](https://cloud.google.com/access-context-manager/docs/browser-attributes) sent by the [Endpoint Verification extension](https://chromewebstore.google.com/detail/endpoint-verification/callobklhcbilhphinckomhgkigmfocg?pli=1)."}, "chromeProfileId": {"description": "Chrome profile ID that is exposed by the Chrome API. It is unique for each device.", "type": "string"}, "lastProfileSyncTime": {"description": "Timestamp in milliseconds since the Unix epoch when the profile/gcm id was last synced.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1BrowserInfo": {"description": "Browser-specific fields reported by the [Endpoint Verification extension](https://chromewebstore.google.com/detail/endpoint-verification/callobklhcbilhphinckomhgkigmfocg?pli=1).", "id": "GoogleAppsCloudidentityDevicesV1BrowserInfo", "properties": {"browserManagementState": {"description": "Output only. <PERSON><PERSON><PERSON>'s management state.", "enum": ["UNSPECIFIED", "UNMANAGED", "MANAGED_BY_OTHER_DOMAIN", "PROFILE_MANAGED", "BROWSER_MANAGED"], "enumDescriptions": ["Management state is not specified.", "Browser/Profile is not managed by any customer.", "Browser/Profile is managed, but by some other customer.", "Profile is managed by customer.", "Browser is managed by customer."], "readOnly": true, "type": "string"}, "browserVersion": {"description": "Version of the request initiating browser. E.g. `91.0.4442.4`.", "type": "string"}, "isBuiltInDnsClientEnabled": {"description": "Current state of [built-in DNS client](https://chromeenterprise.google/policies/#BuiltInDnsClientEnabled).", "type": "boolean"}, "isBulkDataEntryAnalysisEnabled": {"description": "Current state of [bulk data analysis](https://chromeenterprise.google/policies/#OnBulkDataEntryEnterpriseConnector). Set to true if provider list from Chrome is non-empty.", "type": "boolean"}, "isChromeCleanupEnabled": {"description": "Current state of [Chrome Cleanup](https://chromeenterprise.google/policies/#ChromeCleanupEnabled).", "type": "boolean"}, "isChromeRemoteDesktopAppBlocked": {"description": "Current state of [Chrome Remote Desktop app](https://chromeenterprise.google/policies/#URLBlocklist).", "type": "boolean"}, "isFileDownloadAnalysisEnabled": {"description": "Current state of [file download analysis](https://chromeenterprise.google/policies/#OnFileDownloadedEnterpriseConnector). Set to true if provider list from Chrome is non-empty.", "type": "boolean"}, "isFileUploadAnalysisEnabled": {"description": "Current state of [file upload analysis](https://chromeenterprise.google/policies/#OnFileAttachedEnterpriseConnector). Set to true if provider list from Chrome is non-empty.", "type": "boolean"}, "isRealtimeUrlCheckEnabled": {"description": "Current state of [real-time URL check](https://chromeenterprise.google/policies/#EnterpriseRealTimeUrlCheckMode). Set to true if provider list from Chrome is non-empty.", "type": "boolean"}, "isSecurityEventAnalysisEnabled": {"description": "Current state of [security event analysis](https://chromeenterprise.google/policies/#OnSecurityEventEnterpriseConnector). Set to true if provider list from Chrome is non-empty.", "type": "boolean"}, "isSiteIsolationEnabled": {"description": "Current state of [site isolation](https://chromeenterprise.google/policies/?policy=IsolateOrigins).", "type": "boolean"}, "isThirdPartyBlockingEnabled": {"description": "Current state of [third-party blocking](https://chromeenterprise.google/policies/#ThirdPartyBlockingEnabled).", "type": "boolean"}, "passwordProtectionWarningTrigger": {"description": "Current state of [password protection trigger](https://chromeenterprise.google/policies/#PasswordProtectionWarningTrigger).", "enum": ["PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED", "PROTECTION_OFF", "PASSWORD_REUSE", "PHISHING_REUSE"], "enumDescriptions": ["Password protection is not specified.", "Password reuse is never detected.", "Warning is shown when the user reuses their protected password on a non-allowed site.", "Warning is shown when the user reuses their protected password on a phishing site."], "type": "string"}, "safeBrowsingProtectionLevel": {"description": "Current state of [Safe Browsing protection level](https://chromeenterprise.google/policies/#SafeBrowsingProtectionLevel).", "enum": ["SAFE_BROWSING_LEVEL_UNSPECIFIED", "DISABLED", "STANDARD", "ENHANCED"], "enumDescriptions": ["Browser protection level is not specified.", "No protection against dangerous websites, downloads, and extensions.", "Standard protection against websites, downloads, and extensions that are known to be dangerous.", "Faster, proactive protection against dangerous websites, downloads, and extensions."], "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceMetadata": {"description": "Metadata for CancelWipeDevice LRO.", "id": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceRequest": {"description": "Request message for cancelling an unfinished device wipe.", "id": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceRequest", "properties": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceResponse": {"description": "Response message for cancelling an unfinished device wipe.", "id": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceResponse", "properties": {"device": {"$ref": "GoogleAppsCloudidentityDevicesV1Device", "description": "Resultant Device object for the action. Note that asset tags will not be returned in the device object."}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserMetadata": {"description": "Metadata for CancelWipeDeviceUser LRO.", "id": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserRequest": {"description": "Request message for cancelling an unfinished user account wipe.", "id": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserRequest", "properties": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserResponse": {"description": "Response message for cancelling an unfinished user account wipe.", "id": "GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserResponse", "properties": {"deviceUser": {"$ref": "GoogleAppsCloudidentityDevicesV1DeviceUser", "description": "Resultant DeviceUser object for the action."}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CertificateAttributes": {"description": "Stores information about a certificate.", "id": "GoogleAppsCloudidentityDevicesV1CertificateAttributes", "properties": {"certificateTemplate": {"$ref": "GoogleAppsCloudidentityDevicesV1CertificateTemplate", "description": "The X.509 extension for CertificateTemplate."}, "fingerprint": {"description": "The encoded certificate fingerprint.", "type": "string"}, "issuer": {"description": "The name of the issuer of this certificate.", "type": "string"}, "serialNumber": {"description": "Serial number of the certificate, Example: \"*********\".", "type": "string"}, "subject": {"description": "The subject name of this certificate.", "type": "string"}, "thumbprint": {"description": "The certificate thumbprint.", "type": "string"}, "validationState": {"description": "Output only. Validation state of this certificate.", "enum": ["CERTIFICATE_VALIDATION_STATE_UNSPECIFIED", "VALIDATION_SUCCESSFUL", "VALIDATION_FAILED"], "enumDescriptions": ["Default value.", "Certificate validation was successful.", "Certificate validation failed."], "readOnly": true, "type": "string"}, "validityExpirationTime": {"description": "Certificate not valid at or after this timestamp.", "format": "google-datetime", "type": "string"}, "validityStartTime": {"description": "Certificate not valid before this timestamp.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CertificateTemplate": {"description": "CertificateTemplate (v3 Extension in X.509).", "id": "GoogleAppsCloudidentityDevicesV1CertificateTemplate", "properties": {"id": {"description": "The template id of the template. Example: \"*******.4.1.311.21.8.15608621.11768144.5720724.16068415.6889630.81.2472537.7784047\".", "type": "string"}, "majorVersion": {"description": "The Major version of the template. Example: 100.", "format": "int32", "type": "integer"}, "minorVersion": {"description": "The minor version of the template. Example: 12.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ClientState": {"description": "Represents the state associated with an API client calling the Devices API. Resource representing ClientState and supports updates from API users", "id": "GoogleAppsCloudidentityDevicesV1ClientState", "properties": {"assetTags": {"description": "The caller can specify asset tags for this resource", "items": {"type": "string"}, "type": "array"}, "complianceState": {"description": "The compliance state of the resource as specified by the API client.", "enum": ["COMPLIANCE_STATE_UNSPECIFIED", "COMPLIANT", "NON_COMPLIANT"], "enumDescriptions": ["The compliance state of the resource is unknown or unspecified.", "Device is compliant with third party policies", "<PERSON><PERSON> is not compliant with third party policies"], "type": "string"}, "createTime": {"description": "Output only. The time the client state data was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customId": {"description": "This field may be used to store a unique identifier for the API resource within which these CustomAttributes are a field.", "type": "string"}, "etag": {"description": "The token that needs to be passed back for concurrency control in updates. Token needs to be passed back in UpdateRequest", "type": "string"}, "healthScore": {"description": "The Health score of the resource. The Health score is the callers specification of the condition of the device from a usability point of view. For example, a third-party device management provider may specify a health score based on its compliance with organizational policies.", "enum": ["HEALTH_SCORE_UNSPECIFIED", "VERY_POOR", "POOR", "NEUTRAL", "GOOD", "VERY_GOOD"], "enumDescriptions": ["Default value", "The object is in very poor health as defined by the caller.", "The object is in poor health as defined by the caller.", "The object health is neither good nor poor, as defined by the caller.", "The object is in good health as defined by the caller.", "The object is in very good health as defined by the caller."], "type": "string"}, "keyValuePairs": {"additionalProperties": {"$ref": "GoogleAppsCloudidentityDevicesV1CustomAttributeValue"}, "description": "The map of key-value attributes stored by callers specific to a device. The total serialized length of this map may not exceed 10KB. No limit is placed on the number of attributes in a map.", "type": "object"}, "lastUpdateTime": {"description": "Output only. The time the client state data was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "managed": {"description": "The management state of the resource as specified by the API client.", "enum": ["MANAGED_STATE_UNSPECIFIED", "MANAGED", "UNMANAGED"], "enumDescriptions": ["The management state of the resource is unknown or unspecified.", "The resource is managed.", "The resource is not managed."], "type": "string"}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the ClientState in format: `devices/{device}/deviceUsers/{device_user}/clientState/{partner}`, where partner corresponds to the partner storing the data. For partners belonging to the \"BeyondCorp Alliance\", this is the partner ID specified to you by Google. For all other callers, this is a string of the form: `{customer}-suffix`, where `customer` is your customer ID. The *suffix* is any string the caller specifies. This string will be displayed verbatim in the administration console. This suffix is used in setting up Custom Access Levels in Context-Aware Access. Your organization's customer ID can be obtained from the URL: `GET https://www.googleapis.com/admin/directory/v1/customers/my_customer` The `id` field in the response contains the customer ID starting with the letter 'C'. The customer ID to be used in this API is the string after the letter 'C' (not including 'C')", "readOnly": true, "type": "string"}, "ownerType": {"description": "Output only. The owner of the ClientState", "enum": ["OWNER_TYPE_UNSPECIFIED", "OWNER_TYPE_CUSTOMER", "OWNER_TYPE_PARTNER"], "enumDescriptions": ["Unknown owner type", "Customer is the owner", "Partner is the owner"], "readOnly": true, "type": "string"}, "scoreReason": {"description": "A descriptive cause of the health score.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CreateDeviceMetadata": {"description": "Metadata for CreateDevice LRO.", "id": "GoogleAppsCloudidentityDevicesV1CreateDeviceMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1CustomAttributeValue": {"description": "Additional custom attribute values may be one of these types", "id": "GoogleAppsCloudidentityDevicesV1CustomAttributeValue", "properties": {"boolValue": {"description": "Represents a boolean value.", "type": "boolean"}, "numberValue": {"description": "Represents a double value.", "format": "double", "type": "number"}, "stringValue": {"description": "Represents a string value.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1DeleteDeviceMetadata": {"description": "Metadata for DeleteDevice LRO.", "id": "GoogleAppsCloudidentityDevicesV1DeleteDeviceMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1DeleteDeviceUserMetadata": {"description": "Metadata for DeleteDeviceUser LRO.", "id": "GoogleAppsCloudidentityDevicesV1DeleteDeviceUserMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1Device": {"description": " A Device within the Cloud Identity Devices API. Represents a Device known to Google Cloud, independent of the device ownership, type, and whether it is assigned or in use by a user.", "id": "GoogleAppsCloudidentityDevicesV1Device", "properties": {"androidSpecificAttributes": {"$ref": "GoogleAppsCloudidentityDevicesV1AndroidAttributes", "description": "Output only. Attributes specific to Android devices.", "readOnly": true}, "assetTag": {"description": "Asset tag of the device.", "type": "string"}, "basebandVersion": {"description": "Output only. Baseband version of the device.", "readOnly": true, "type": "string"}, "bootloaderVersion": {"description": "Output only. Device bootloader version. Example: 0.6.7.", "readOnly": true, "type": "string"}, "brand": {"description": "Output only. Device brand. Example: Samsung.", "readOnly": true, "type": "string"}, "buildNumber": {"description": "Output only. Build number of the device.", "readOnly": true, "type": "string"}, "compromisedState": {"description": "Output only. Represents whether the Device is compromised.", "enum": ["COMPROMISED_STATE_UNSPECIFIED", "COMPROMISED", "UNCOMPROMISED"], "enumDescriptions": ["Default value.", "The device is compromised (currently, this means Android device is rooted).", "The device is safe (currently, this means Android device is unrooted)."], "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. When the Company-Owned device was imported. This field is empty for BYOD devices.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deviceId": {"description": "Unique identifier for the device.", "type": "string"}, "deviceType": {"description": "Output only. Type of device.", "enum": ["DEVICE_TYPE_UNSPECIFIED", "ANDROID", "IOS", "GOOGLE_SYNC", "WINDOWS", "MAC_OS", "LINUX", "CHROME_OS"], "enumDescriptions": ["Unknown device type", "Device is an Android device", "Device is an iOS device", "Device is a Google Sync device.", "Device is a Windows device.", "Device is a MacOS device.", "Device is a Linux device.", "Device is a ChromeOS device."], "readOnly": true, "type": "string"}, "enabledDeveloperOptions": {"description": "Output only. Whether developer options is enabled on device.", "readOnly": true, "type": "boolean"}, "enabledUsbDebugging": {"description": "Output only. Whether USB debugging is enabled on device.", "readOnly": true, "type": "boolean"}, "encryptionState": {"description": "Output only. Device encryption state.", "enum": ["ENCRYPTION_STATE_UNSPECIFIED", "UNSUPPORTED_BY_DEVICE", "ENCRYPTED", "NOT_ENCRYPTED"], "enumDescriptions": ["Encryption Status is not set.", "Device doesn't support encryption.", "Device is encrypted.", "Device is not encrypted."], "readOnly": true, "type": "string"}, "endpointVerificationSpecificAttributes": {"$ref": "GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes", "description": "Output only. Attributes specific to [Endpoint Verification](https://cloud.google.com/endpoint-verification/docs/overview) devices.", "readOnly": true}, "hostname": {"description": "Host name of the device.", "type": "string"}, "imei": {"description": "Output only. IMEI number of device if GSM device; empty otherwise.", "readOnly": true, "type": "string"}, "kernelVersion": {"description": "Output only. Kernel version of the device.", "readOnly": true, "type": "string"}, "lastSyncTime": {"description": "Most recent time when device synced with this service.", "format": "google-datetime", "type": "string"}, "managementState": {"description": "Output only. Management state of the device", "enum": ["MANAGEMENT_STATE_UNSPECIFIED", "APPROVED", "BLOCKED", "PENDING", "UNPROVISIONED", "WIPING", "WIPED"], "enumDescriptions": ["Default value. This value is unused.", "Device is approved.", "<PERSON><PERSON> is blocked.", "Device is pending approval.", "The device is not provisioned. <PERSON><PERSON> will start from this state until some action is taken (i.e. a user starts using the device).", "Data and settings on the device are being removed.", "All data and settings on the device are removed."], "readOnly": true, "type": "string"}, "manufacturer": {"description": "Output only. Device manufacturer. Example: Motorola.", "readOnly": true, "type": "string"}, "meid": {"description": "Output only. MEID number of device if CDMA device; empty otherwise.", "readOnly": true, "type": "string"}, "model": {"description": "Output only. Model name of device. Example: Pixel 3.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Device in format: `devices/{device}`, where device is the unique id assigned to the Device. Important: Device API scopes require that you use domain-wide delegation to access the API. For more information, see [Set up the Devices API](https://cloud.google.com/identity/docs/how-to/setup-devices).", "readOnly": true, "type": "string"}, "networkOperator": {"description": "Output only. Mobile or network operator of device, if available.", "readOnly": true, "type": "string"}, "osVersion": {"description": "Output only. OS version of the device. Example: Android 8.1.0.", "readOnly": true, "type": "string"}, "otherAccounts": {"description": "Output only. Domain name for Google accounts on device. Type for other accounts on device. On Android, will only be populated if |ownership_privilege| is |PROFILE_OWNER| or |DEVICE_OWNER|. Does not include the account signed in to the device policy app if that account's domain has only one account. Examples: \"com.example\", \"xyz.com\".", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "ownerType": {"description": "Output only. Whether the device is owned by the company or an individual", "enum": ["DEVICE_OWNERSHIP_UNSPECIFIED", "COMPANY", "BYOD"], "enumDescriptions": ["Default value. The value is unused.", "Company owns the device.", "Bring Your Own Device (i.e. individual owns the device)"], "readOnly": true, "type": "string"}, "releaseVersion": {"description": "Output only. OS release version. Example: 6.0.", "readOnly": true, "type": "string"}, "securityPatchTime": {"description": "Output only. OS security patch update time on device.", "format": "google-datetime", "readOnly": true, "type": "string"}, "serialNumber": {"description": "Serial Number of device. Example: HT82V1A01076.", "type": "string"}, "unifiedDeviceId": {"description": "Output only. Unified device id of the device.", "readOnly": true, "type": "string"}, "wifiMacAddresses": {"description": "WiFi MAC addresses of device.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1DeviceUser": {"description": "Represents a user's use of a Device in the Cloud Identity Devices API. A DeviceUser is a resource representing a user's use of a Device", "id": "GoogleAppsCloudidentityDevicesV1DeviceUser", "properties": {"compromisedState": {"description": "Compromised State of the DeviceUser object", "enum": ["COMPROMISED_STATE_UNSPECIFIED", "COMPROMISED", "NOT_COMPROMISED"], "enumDescriptions": ["Compromised state of Device User account is unknown or unspecified.", "Device User Account is compromised.", "Device User Account is not compromised."], "type": "string"}, "createTime": {"description": "When the user first signed in to the device", "format": "google-datetime", "type": "string"}, "firstSyncTime": {"description": "Output only. Most recent time when user registered with this service.", "format": "google-datetime", "readOnly": true, "type": "string"}, "languageCode": {"description": "Output only. Default locale used on device, in IETF BCP-47 format.", "readOnly": true, "type": "string"}, "lastSyncTime": {"description": "Output only. Last time when user synced with policies.", "format": "google-datetime", "readOnly": true, "type": "string"}, "managementState": {"description": "Output only. Management state of the user on the device.", "enum": ["MANAGEMENT_STATE_UNSPECIFIED", "WIPING", "WIPED", "APPROVED", "BLOCKED", "PENDING_APPROVAL", "UNENROLLED"], "enumDescriptions": ["Default value. This value is unused.", "This user's data and profile is being removed from the device.", "This user's data and profile is removed from the device.", "User is approved to access data on the device.", "User is blocked from accessing data on the device.", "User is awaiting approval.", "User is unenrolled from Advanced Windows Management, but the Windows account is still intact."], "readOnly": true, "type": "string"}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the DeviceUser in format: `devices/{device}/deviceUsers/{device_user}`, where `device_user` uniquely identifies a user's use of a device.", "readOnly": true, "type": "string"}, "passwordState": {"description": "Password state of the DeviceUser object", "enum": ["PASSWORD_STATE_UNSPECIFIED", "PASSWORD_SET", "PASSWORD_NOT_SET"], "enumDescriptions": ["Password state not set.", "Password set in object.", "Password not set in object."], "type": "string"}, "userAgent": {"description": "Output only. User agent on the device for this specific user", "readOnly": true, "type": "string"}, "userEmail": {"description": "Email address of the user registered on the device.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes": {"description": "Resource representing the [Endpoint Verification-specific attributes](https://cloud.google.com/endpoint-verification/docs/device-information) of a device.", "id": "GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes", "properties": {"additionalSignals": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "[Additional signals](https://cloud.google.com/endpoint-verification/docs/device-information) reported by Endpoint Verification. It includes the following attributes: * Non-configurable attributes: hotfixes, av_installed, av_enabled, windows_domain_name, is_os_native_firewall_enabled, and is_secure_boot_enabled. * [Configurable attributes](https://cloud.google.com/endpoint-verification/docs/collect-config-attributes): file, folder, and binary attributes; registry entries; and properties in a plist.", "type": "object"}, "browserAttributes": {"description": "Details of browser profiles reported by Endpoint Verification.", "items": {"$ref": "GoogleAppsCloudidentityDevicesV1BrowserAttributes"}, "type": "array"}, "certificateAttributes": {"description": "Details of certificates.", "items": {"$ref": "GoogleAppsCloudidentityDevicesV1CertificateAttributes"}, "type": "array"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ListClientStatesResponse": {"description": "Response message that is returned in ListClientStates.", "id": "GoogleAppsCloudidentityDevicesV1ListClientStatesResponse", "properties": {"clientStates": {"description": "Client states meeting the list restrictions.", "items": {"$ref": "GoogleAppsCloudidentityDevicesV1ClientState"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. Empty if there are no more results.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ListDeviceUsersResponse": {"description": "Response message that is returned from the ListDeviceUsers method.", "id": "GoogleAppsCloudidentityDevicesV1ListDeviceUsersResponse", "properties": {"deviceUsers": {"description": "Devices meeting the list restrictions.", "items": {"$ref": "GoogleAppsCloudidentityDevicesV1DeviceUser"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. Empty if there are no more results.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ListDevicesResponse": {"description": "Response message that is returned from the ListDevices method.", "id": "GoogleAppsCloudidentityDevicesV1ListDevicesResponse", "properties": {"devices": {"description": "Devices meeting the list restrictions.", "items": {"$ref": "GoogleAppsCloudidentityDevicesV1Device"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. Empty if there are no more results.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1ListEndpointAppsMetadata": {"description": "Metadata for ListEndpointApps LRO.", "id": "GoogleAppsCloudidentityDevicesV1ListEndpointAppsMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1LookupSelfDeviceUsersResponse": {"description": "Response containing resource names of the DeviceUsers associated with the caller's credentials.", "id": "GoogleAppsCloudidentityDevicesV1LookupSelfDeviceUsersResponse", "properties": {"customer": {"description": "The customer resource name that may be passed back to other Devices API methods such as List, Get, etc.", "type": "string"}, "names": {"description": "[Resource names](https://cloud.google.com/apis/design/resource_names) of the DeviceUsers in the format: `devices/{device}/deviceUsers/{user_resource}`, where device is the unique ID assigned to a Device and user_resource is the unique user ID", "items": {"type": "string"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. Empty if there are no more results.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1SignoutDeviceUserMetadata": {"description": "Metadata for SignoutDeviceUser LRO.", "id": "GoogleAppsCloudidentityDevicesV1SignoutDeviceUserMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1UpdateClientStateMetadata": {"description": "Metadata for UpdateClientState LRO.", "id": "GoogleAppsCloudidentityDevicesV1UpdateClientStateMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1UpdateDeviceMetadata": {"description": "Metadata for UpdateDevice LRO.", "id": "GoogleAppsCloudidentityDevicesV1UpdateDeviceMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1WipeDeviceMetadata": {"description": "Metadata for WipeDevice LRO.", "id": "GoogleAppsCloudidentityDevicesV1WipeDeviceMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1WipeDeviceRequest": {"description": "Request message for wiping all data on the device.", "id": "GoogleAppsCloudidentityDevicesV1WipeDeviceRequest", "properties": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "type": "string"}, "removeResetLock": {"description": "Optional. Specifies if a user is able to factory reset a device after a Device Wipe. On iOS, this is called \"Activation Lock\", while on Android, this is known as \"Factory Reset Protection\". If true, this protection will be removed from the device, so that a user can successfully factory reset. If false, the setting is untouched on the device.", "type": "boolean"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1WipeDeviceResponse": {"description": "Response message for wiping all data on the device.", "id": "GoogleAppsCloudidentityDevicesV1WipeDeviceResponse", "properties": {"device": {"$ref": "GoogleAppsCloudidentityDevicesV1Device", "description": "Resultant Device object for the action. Note that asset tags will not be returned in the device object."}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1WipeDeviceUserMetadata": {"description": "Metadata for WipeDeviceUser LRO.", "id": "GoogleAppsCloudidentityDevicesV1WipeDeviceUserMetadata", "properties": {}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1WipeDeviceUserRequest": {"description": "Request message for starting an account wipe on device.", "id": "GoogleAppsCloudidentityDevicesV1WipeDeviceUserRequest", "properties": {"customer": {"description": "Optional. [Resource name](https://cloud.google.com/apis/design/resource_names) of the customer. If you're using this API for your own organization, use `customers/my_customer` If you're using this API to manage another organization, use `customers/{customer}`, where customer is the customer to whom the device belongs.", "type": "string"}}, "type": "object"}, "GoogleAppsCloudidentityDevicesV1WipeDeviceUserResponse": {"description": "Response message for wiping the user's account from the device.", "id": "GoogleAppsCloudidentityDevicesV1WipeDeviceUserResponse", "properties": {"deviceUser": {"$ref": "GoogleAppsCloudidentityDevicesV1DeviceUser", "description": "Resultant DeviceUser object for the action."}}, "type": "object"}, "Group": {"description": "A group within the Cloud Identity Groups API. A `Group` is a collection of entities, where each entity is either a user, another group, or a service account.", "id": "Group", "properties": {"additionalGroupKeys": {"description": "Output only. Additional group keys associated with the Group.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The time when the `Group` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "An extended description to help users determine the purpose of a `Group`. Must not be longer than 4,096 characters.", "type": "string"}, "displayName": {"description": "The display name of the `Group`.", "type": "string"}, "dynamicGroupMetadata": {"$ref": "DynamicGroupMetadata", "description": "Optional. Dynamic group metadata like queries and status."}, "groupKey": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Required. The `EntityKey` of the `Group`."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Required. One or more label entries that apply to the Group. Labels contain a key with an empty value. Google Groups are the default type of group and have a label with a key of `cloudidentity.googleapis.com/groups.discussion_forum` and an empty value. Existing Google Groups can have an additional label with a key of `cloudidentity.googleapis.com/groups.security` and an empty value added to them. **This is an immutable change and the security label cannot be removed once added.** Dynamic groups have a label with a key of `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups for Cloud Search have a label with a key of `system/groups/external` and an empty value. Google Groups can be [locked](https://support.google.com/a?p=locked-groups). To lock a group, add a label with a key of `cloudidentity.googleapis.com/groups.locked` and an empty value. Doing so locks the group. To unlock the group, remove this label.", "type": "object"}, "name": {"description": "Output only. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Group`. Shall be of the form `groups/{group}`.", "readOnly": true, "type": "string"}, "parent": {"description": "Required. Immutable. The resource name of the entity under which this `Group` resides in the Cloud Identity resource hierarchy. Must be of the form `identitysources/{identity_source}` for external [identity-mapped groups](https://support.google.com/a/answer/9039510) or `customers/{customer_id}` for Google Groups. The `customer_id` must begin with \"C\" (for example, 'C046psxkn'). [Find your customer ID.] (https://support.google.com/cloudidentity/answer/10070793)", "type": "string"}, "updateTime": {"description": "Output only. The time when the `Group` was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GroupRelation": {"description": "Message representing a transitive group of a user or a group.", "id": "GroupRelation", "properties": {"displayName": {"description": "Display name for this group.", "type": "string"}, "group": {"description": "Resource name for this group.", "type": "string"}, "groupKey": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Entity key has an id and a namespace. In case of discussion forums, the id will be an email address without a namespace."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels for Group resource.", "type": "object"}, "relationType": {"description": "The relation between the member and the transitive group.", "enum": ["RELATION_TYPE_UNSPECIFIED", "DIRECT", "INDIRECT", "DIRECT_AND_INDIRECT"], "enumDescriptions": ["The relation type is undefined or undetermined.", "The two entities have only a direct membership with each other.", "The two entities have only an indirect membership with each other.", "The two entities have both a direct and an indirect membership with each other."], "type": "string"}, "roles": {"description": "Membership roles of the member for the group.", "items": {"$ref": "TransitiveMembershipRole"}, "type": "array"}}, "type": "object"}, "IdpCredential": {"description": "Credential for verifying signatures produced by the Identity Provider.", "id": "IdpCredential", "properties": {"dsaKeyInfo": {"$ref": "DsaPublicKeyInfo", "description": "Output only. Information of a DSA public key.", "readOnly": true}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the credential.", "readOnly": true, "type": "string"}, "rsaKeyInfo": {"$ref": "RsaPublicKeyInfo", "description": "Output only. Information of a RSA public key.", "readOnly": true}, "updateTime": {"description": "Output only. Time when the `IdpCredential` was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InboundSamlSsoProfile": {"description": "A [SAML 2.0](https://www.oasis-open.org/standards#samlv2.0) federation between a Google enterprise customer and a SAML identity provider.", "id": "InboundSamlSsoProfile", "properties": {"customer": {"description": "Immutable. The customer. For example: `customers/C0123abc`.", "type": "string"}, "displayName": {"description": "Human-readable name of the SAML SSO profile.", "type": "string"}, "idpConfig": {"$ref": "SamlIdpConfig", "description": "SAML identity provider configuration."}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the SAML SSO profile.", "readOnly": true, "type": "string"}, "spConfig": {"$ref": "SamlSpConfig", "description": "SAML service provider configuration for this SAML SSO profile. These are the service provider details provided by Google that should be configured on the corresponding identity provider."}}, "type": "object"}, "InboundSsoAssignment": {"description": "Targets with \"set\" SSO assignments and their respective assignments.", "id": "InboundSsoAssignment", "properties": {"customer": {"description": "Immutable. The customer. For example: `customers/C0123abc`.", "type": "string"}, "name": {"description": "Output only. [Resource name](https://cloud.google.com/apis/design/resource_names) of the Inbound SSO Assignment.", "readOnly": true, "type": "string"}, "rank": {"description": "Must be zero (which is the default value so it can be omitted) for assignments with `target_org_unit` set and must be greater-than-or-equal-to one for assignments with `target_group` set.", "format": "int32", "type": "integer"}, "samlSsoInfo": {"$ref": "SamlSsoInfo", "description": "SAML SSO details. Must be set if and only if `sso_mode` is set to `SAML_SSO`."}, "signInBehavior": {"$ref": "SignInBehavior", "description": "Assertions about users assigned to an IdP will always be accepted from that IdP. This controls whether/when Google should redirect a user to the IdP. Unset (defaults) is the recommended configuration."}, "ssoMode": {"description": "Inbound SSO behavior.", "enum": ["SSO_MODE_UNSPECIFIED", "SSO_OFF", "SAML_SSO", "DOMAIN_WIDE_SAML_IF_ENABLED"], "enumDescriptions": ["Not allowed.", "Disable SSO for the targeted users.", "Use an external SAML Identity Provider for SSO for the targeted users.", "Use the domain-wide SAML Identity Provider for the targeted users if one is configured; otherwise, this is equivalent to `SSO_OFF`. Note that this will also be equivalent to `SSO_OFF` if/when support for domain-wide SAML is removed. Google may disallow this mode at that point and existing assignments with this mode may be automatically changed to `SSO_OFF`."], "type": "string"}, "targetGroup": {"description": "Immutable. Must be of the form `groups/{group}`.", "type": "string"}, "targetOrgUnit": {"description": "Immutable. Must be of the form `orgUnits/{org_unit}`.", "type": "string"}}, "type": "object"}, "IsInvitableUserResponse": {"description": "Response for IsInvitableUser RPC.", "id": "IsInvitableUserResponse", "properties": {"isInvitableUser": {"description": "Returns true if the email address is invitable.", "type": "boolean"}}, "type": "object"}, "ListGroupsResponse": {"description": "Response message for ListGroups operation.", "id": "ListGroupsResponse", "properties": {"groups": {"description": "Groups returned in response to list request. The results are not sorted.", "items": {"$ref": "Group"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results available for listing.", "type": "string"}}, "type": "object"}, "ListIdpCredentialsResponse": {"description": "Response of the InboundSamlSsoProfilesService.ListIdpCredentials method.", "id": "ListIdpCredentialsResponse", "properties": {"idpCredentials": {"description": "The IdpCredentials from the specified InboundSamlSsoProfile.", "items": {"$ref": "IdpCredential"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListInboundSamlSsoProfilesResponse": {"description": "Response of the InboundSamlSsoProfilesService.ListInboundSamlSsoProfiles method.", "id": "ListInboundSamlSsoProfilesResponse", "properties": {"inboundSamlSsoProfiles": {"description": "List of InboundSamlSsoProfiles.", "items": {"$ref": "InboundSamlSsoProfile"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListInboundSsoAssignmentsResponse": {"description": "Response of the InboundSsoAssignmentsService.ListInboundSsoAssignments method.", "id": "ListInboundSsoAssignmentsResponse", "properties": {"inboundSsoAssignments": {"description": "The assignments.", "items": {"$ref": "InboundSsoAssignment"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListMembershipsResponse": {"description": "The response message for MembershipsService.ListMemberships.", "id": "ListMembershipsResponse", "properties": {"memberships": {"description": "The `Membership`s under the specified `parent`.", "items": {"$ref": "Membership"}, "type": "array"}, "nextPageToken": {"description": "A continuation token to retrieve the next page of results, or empty if there are no more results available.", "type": "string"}}, "type": "object"}, "ListPoliciesResponse": {"description": "The response message for PoliciesService.ListPolicies.", "id": "ListPoliciesResponse", "properties": {"nextPageToken": {"description": "The pagination token to retrieve the next page of results. If this field is empty, there are no subsequent pages.", "type": "string"}, "policies": {"description": "The results", "items": {"$ref": "Policy"}, "type": "array"}}, "type": "object"}, "ListUserInvitationsResponse": {"description": "Response message for UserInvitation listing request.", "id": "ListUserInvitationsResponse", "properties": {"nextPageToken": {"description": "The token for the next page. If not empty, indicates that there may be more `UserInvitation` resources that match the listing request; this value can be used in a subsequent ListUserInvitationsRequest to get continued results with the current list call.", "type": "string"}, "userInvitations": {"description": "The list of UserInvitation resources.", "items": {"$ref": "UserInvitation"}, "type": "array"}}, "type": "object"}, "LookupGroupNameResponse": {"description": "The response message for GroupsService.LookupGroupName.", "id": "LookupGroupNameResponse", "properties": {"name": {"description": "The [resource name](https://cloud.google.com/apis/design/resource_names) of the looked-up `Group`.", "type": "string"}}, "type": "object"}, "LookupMembershipNameResponse": {"description": "The response message for MembershipsService.LookupMembershipName.", "id": "LookupMembershipNameResponse", "properties": {"name": {"description": "The [resource name](https://cloud.google.com/apis/design/resource_names) of the looked-up `Membership`. Must be of the form `groups/{group}/memberships/{membership}`.", "type": "string"}}, "type": "object"}, "MemberRelation": {"description": "Message representing a transitive membership of a group.", "id": "MemberRelation", "properties": {"member": {"description": "Resource name for this member.", "type": "string"}, "preferredMemberKey": {"description": "Entity key has an id and a namespace. In case of discussion forums, the id will be an email address without a namespace.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "relationType": {"description": "The relation between the group and the transitive member.", "enum": ["RELATION_TYPE_UNSPECIFIED", "DIRECT", "INDIRECT", "DIRECT_AND_INDIRECT"], "enumDescriptions": ["The relation type is undefined or undetermined.", "The two entities have only a direct membership with each other.", "The two entities have only an indirect membership with each other.", "The two entities have both a direct and an indirect membership with each other."], "type": "string"}, "roles": {"description": "The membership role details (i.e name of role and expiry time).", "items": {"$ref": "TransitiveMembershipRole"}, "type": "array"}}, "type": "object"}, "MemberRestriction": {"description": "The definition of MemberRestriction", "id": "MemberRestriction", "properties": {"evaluation": {"$ref": "RestrictionEvaluation", "description": "The evaluated state of this restriction on a group."}, "query": {"description": "Member Restriction as defined by CEL expression. Supported restrictions are: `member.customer_id` and `member.type`. Valid values for `member.type` are `1`, `2` and `3`. They correspond to USER, SERVICE_ACCOUNT, and GROUP respectively. The value for `member.customer_id` only supports `groupCustomerId()` currently which means the customer id of the group will be used for restriction. Supported operators are `&&`, `||` and `==`, corresponding to AND, OR, and EQUAL. Examples: Allow only service accounts of given customer to be members. `member.type == 2 && member.customer_id == groupCustomerId()` Allow only users or groups to be members. `member.type == 1 || member.type == 3`", "type": "string"}}, "type": "object"}, "Membership": {"description": "A membership within the Cloud Identity Groups API. A `Membership` defines a relationship between a `Group` and an entity belonging to that `Group`, referred to as a \"member\".", "id": "Membership", "properties": {"createTime": {"description": "Output only. The time when the `Membership` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deliverySetting": {"description": "Output only. Delivery setting associated with the membership.", "enum": ["DELIVERY_SETTING_UNSPECIFIED", "ALL_MAIL", "DIGEST", "DAILY", "NONE", "DISABLED"], "enumDescriptions": ["Default. Should not be used.", "Represents each mail should be delivered", "Represents 1 email for every 25 messages.", "Represents daily summary of messages.", "Represents no delivery.", "Represents disabled state."], "readOnly": true, "type": "string"}, "name": {"description": "Output only. The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Membership`. Shall be of the form `groups/{group}/memberships/{membership}`.", "readOnly": true, "type": "string"}, "preferredMemberKey": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Required. Immutable. The `EntityKey` of the member."}, "roles": {"description": "The `MembershipRole`s that apply to the `Membership`. If unspecified, defaults to a single `MembershipRole` with `name` `MEMBER`. Must not contain duplicate `MembershipRole`s with the same `name`.", "items": {"$ref": "MembershipRole"}, "type": "array"}, "type": {"description": "Output only. The type of the membership.", "enum": ["TYPE_UNSPECIFIED", "USER", "SERVICE_ACCOUNT", "GROUP", "SHARED_DRIVE", "CBCM_BROWSER", "OTHER"], "enumDescriptions": ["Default. Should not be used.", "Represents user type.", "Represents service account type.", "Represents group type.", "Represents Shared drive.", "Represents a CBCM-managed Chrome Browser type.", "Represents other type."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the `Membership` was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MembershipAdjacencyList": {"description": "Membership graph's path information as an adjacency list.", "id": "MembershipAdjacencyList", "properties": {"edges": {"description": "Each edge contains information about the member that belongs to this group. Note: Fields returned here will help identify the specific Membership resource (e.g `name`, `preferred_member_key` and `role`), but may not be a comprehensive list of all fields.", "items": {"$ref": "Membership"}, "type": "array"}, "group": {"description": "Resource name of the group that the members belong to.", "type": "string"}}, "type": "object"}, "MembershipRelation": {"description": "Message containing membership relation.", "id": "MembershipRelation", "properties": {"description": {"description": "An extended description to help users determine the purpose of a `Group`.", "type": "string"}, "displayName": {"description": "The display name of the `Group`.", "type": "string"}, "group": {"description": "The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Group`. Shall be of the form `groups/{group_id}`.", "type": "string"}, "groupKey": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The `EntityKey` of the `Group`."}, "labels": {"additionalProperties": {"type": "string"}, "description": "One or more label entries that apply to the Group. Currently supported labels contain a key with an empty value.", "type": "object"}, "membership": {"description": "The [resource name](https://cloud.google.com/apis/design/resource_names) of the `Membership`. Shall be of the form `groups/{group_id}/memberships/{membership_id}`.", "type": "string"}, "roles": {"description": "The `MembershipRole`s that apply to the `Membership`.", "items": {"$ref": "MembershipRole"}, "type": "array"}}, "type": "object"}, "MembershipRole": {"description": "A membership role within the Cloud Identity Groups API. A `MembershipRole` defines the privileges granted to a `Membership`.", "id": "MembershipRole", "properties": {"expiryDetail": {"$ref": "ExpiryDetail", "description": "The expiry details of the `MembershipRole`. Expiry details are only supported for `MEMBER` `MembershipRoles`. May be set if `name` is `MEMBER`. Must not be set if `name` is any other value."}, "name": {"description": "The name of the `MembershipRole`. Must be one of `OWNER`, `MANAGER`, `MEMBER`.", "type": "string"}, "restrictionEvaluations": {"$ref": "RestrictionEvaluations", "description": "Evaluations of restrictions applied to parent group on this membership."}}, "type": "object"}, "MembershipRoleRestrictionEvaluation": {"description": "The evaluated state of this restriction.", "id": "MembershipRoleRestrictionEvaluation", "properties": {"state": {"description": "Output only. The current state of the restriction", "enum": ["STATE_UNSPECIFIED", "COMPLIANT", "FORWARD_COMPLIANT", "NON_COMPLIANT", "EVALUATING"], "enumDescriptions": ["Default. Should not be used.", "The member adheres to the parent group's restriction.", "The group-group membership might be currently violating some parent group's restriction but in future, it will never allow any new member in the child group which can violate parent group's restriction.", "The member violates the parent group's restriction.", "The state of the membership is under evaluation."], "readOnly": true, "type": "string"}}, "type": "object"}, "ModifyMembershipRolesRequest": {"description": "The request message for MembershipsService.ModifyMembershipRoles.", "id": "ModifyMembershipRolesRequest", "properties": {"addRoles": {"description": "The `MembershipRole`s to be added. Adding or removing roles in the same request as updating roles is not supported. Must not be set if `update_roles_params` is set.", "items": {"$ref": "MembershipRole"}, "type": "array"}, "removeRoles": {"description": "The `name`s of the `MembershipRole`s to be removed. Adding or removing roles in the same request as updating roles is not supported. It is not possible to remove the `MEMBER` `MembershipRole`. If you wish to delete a `Membership`, call MembershipsService.DeleteMembership instead. Must not contain `MEMBER`. Must not be set if `update_roles_params` is set.", "items": {"type": "string"}, "type": "array"}, "updateRolesParams": {"description": "The `MembershipRole`s to be updated. Updating roles in the same request as adding or removing roles is not supported. Must not be set if either `add_roles` or `remove_roles` is set.", "items": {"$ref": "UpdateMembershipRolesParams"}, "type": "array"}}, "type": "object"}, "ModifyMembershipRolesResponse": {"description": "The response message for MembershipsService.ModifyMembershipRoles.", "id": "ModifyMembershipRolesResponse", "properties": {"membership": {"$ref": "Membership", "description": "The `Membership` resource after modifying its `MembershipRole`s."}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "Policy": {"description": "A Policy resource binds an instance of a single Setting with the scope of a PolicyQuery. The Setting instance will be applied to all entities that satisfy the query.", "id": "Policy", "properties": {"customer": {"description": "Immutable. Customer that the Policy belongs to. The value is in the format 'customers/{customerId}'. The `customerId` must begin with \"C\" To find your customer ID in Admin Console see https://support.google.com/a/answer/10070793.", "type": "string"}, "name": {"description": "Output only. Identifier. The [resource name](https://cloud.google.com/apis/design/resource_names) of the Policy. Format: policies/{policy}.", "readOnly": true, "type": "string"}, "policyQuery": {"$ref": "PolicyQuery", "description": "Required. The PolicyQuery the Setting applies to."}, "setting": {"$ref": "Setting", "description": "Required. The Setting configured by this Policy."}, "type": {"description": "Output only. The type of the policy.", "enum": ["POLICY_TYPE_UNSPECIFIED", "SYSTEM", "ADMIN"], "enumDescriptions": ["Unspecified policy type.", "Policy type denoting the system-configured policies.", "Policy type denoting the admin-configurable policies."], "readOnly": true, "type": "string"}}, "type": "object"}, "PolicyQuery": {"description": "PolicyQuery", "id": "PolicyQuery", "properties": {"group": {"description": "Immutable. The group that the query applies to. This field is only set if there is a single value for group that satisfies all clauses of the query. If no group applies, this will be the empty string.", "type": "string"}, "orgUnit": {"description": "Required. Immutable. Non-empty default. The OrgUnit the query applies to. This field is only set if there is a single value for org_unit that satisfies all clauses of the query.", "type": "string"}, "query": {"description": "Immutable. The CEL query that defines which entities the Policy applies to (ex. a User entity). For details about CEL see https://opensource.google.com/projects/cel. The OrgUnits the Policy applies to are represented by a clause like so: entity.org_units.exists(org_unit, org_unit.org_unit_id == orgUnitId('{orgUnitId}')) The Group the Policy applies to are represented by a clause like so: entity.groups.exists(group, group.group_id == groupId('{groupId}')) The Licenses the Policy applies to are represented by a clause like so: entity.licenses.exists(license, license in ['/product/{productId}/sku/{skuId}']) The above clauses can be present in any combination, and used in conjunction with the &&, || and ! operators. The org_unit and group fields below are helper fields that contain the corresponding value(s) as the query to make the query easier to use.", "type": "string"}, "sortOrder": {"description": "Output only. The decimal sort order of this PolicyQuery. The value is relative to all other policies with the same setting type for the customer. (There are no duplicates within this set).", "format": "double", "readOnly": true, "type": "number"}}, "type": "object"}, "RestrictionEvaluation": {"description": "The evaluated state of this restriction.", "id": "RestrictionEvaluation", "properties": {"state": {"description": "Output only. The current state of the restriction", "enum": ["STATE_UNSPECIFIED", "EVALUATING", "COMPLIANT", "FORWARD_COMPLIANT", "NON_COMPLIANT"], "enumDescriptions": ["Default. Should not be used.", "The restriction state is currently being evaluated.", "All transitive memberships are adhering to restriction.", "Some transitive memberships violate the restriction. No new violating memberships can be added.", "Some transitive memberships violate the restriction. New violating direct memberships will be denied while indirect memberships may be added."], "readOnly": true, "type": "string"}}, "type": "object"}, "RestrictionEvaluations": {"description": "Evaluations of restrictions applied to parent group on this membership.", "id": "RestrictionEvaluations", "properties": {"memberRestrictionEvaluation": {"$ref": "MembershipRoleRestrictionEvaluation", "description": "Evaluation of the member restriction applied to this membership. Empty if the user lacks permission to view the restriction evaluation."}}, "type": "object"}, "RsaPublicKeyInfo": {"description": "Information of a RSA public key.", "id": "RsaPublicKeyInfo", "properties": {"keySize": {"description": "Key size in bits (size of the modulus).", "format": "int32", "type": "integer"}}, "type": "object"}, "SamlIdpConfig": {"description": "SAML IDP (identity provider) configuration.", "id": "SamlIdpConfig", "properties": {"changePasswordUri": {"description": "The **Change Password URL** of the identity provider. Users will be sent to this URL when changing their passwords at `myaccount.google.com`. This takes precedence over the change password URL configured at customer-level. Must use `HTTPS`.", "type": "string"}, "entityId": {"description": "Required. The SAML **Entity ID** of the identity provider.", "type": "string"}, "logoutRedirectUri": {"description": "The **Logout Redirect URL** (sign-out page URL) of the identity provider. When a user clicks the sign-out link on a Google page, they will be redirected to this URL. This is a pure redirect with no attached SAML `LogoutRequest` i.e. SAML single logout is not supported. Must use `HTTPS`.", "type": "string"}, "singleSignOnServiceUri": {"description": "Required. The `SingleSignOnService` endpoint location (sign-in page URL) of the identity provider. This is the URL where the `AuthnRequest` will be sent. Must use `HTTPS`. Assumed to accept the `HTTP-Redirect` binding.", "type": "string"}}, "type": "object"}, "SamlSpConfig": {"description": "SAML SP (service provider) configuration.", "id": "SamlSpConfig", "properties": {"assertionConsumerServiceUri": {"description": "Output only. The SAML **Assertion Consumer Service (ACS) URL** to be used for the IDP-initiated login. Assumed to accept response messages via the `HTTP-POST` binding.", "readOnly": true, "type": "string"}, "entityId": {"description": "Output only. The SAML **Entity ID** for this service provider.", "readOnly": true, "type": "string"}}, "type": "object"}, "SamlSsoInfo": {"description": "Details that are applicable when `sso_mode` == `SAML_SSO`.", "id": "SamlSsoInfo", "properties": {"inboundSamlSsoProfile": {"description": "Required. Name of the `InboundSamlSsoProfile` to use. Must be of the form `inboundSamlSsoProfiles/{inbound_saml_sso_profile}`. ", "type": "string"}}, "type": "object"}, "SearchDirectGroupsResponse": {"description": "The response message for MembershipsService.SearchDirectGroups.", "id": "SearchDirectGroupsResponse", "properties": {"memberships": {"description": "List of direct groups satisfying the query.", "items": {"$ref": "MembershipRelation"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results available for listing.", "type": "string"}}, "type": "object"}, "SearchGroupsResponse": {"description": "The response message for GroupsService.SearchGroups.", "id": "SearchGroupsResponse", "properties": {"groups": {"description": "The `Group` resources that match the search query.", "items": {"$ref": "Group"}, "type": "array"}, "nextPageToken": {"description": "A continuation token to retrieve the next page of results, or empty if there are no more results available.", "type": "string"}}, "type": "object"}, "SearchTransitiveGroupsResponse": {"description": "The response message for MembershipsService.SearchTransitiveGroups.", "id": "SearchTransitiveGroupsResponse", "properties": {"memberships": {"description": "List of transitive groups satisfying the query.", "items": {"$ref": "GroupRelation"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results available for listing.", "type": "string"}}, "type": "object"}, "SearchTransitiveMembershipsResponse": {"description": "The response message for MembershipsService.SearchTransitiveMemberships.", "id": "SearchTransitiveMembershipsResponse", "properties": {"memberships": {"description": "List of transitive members satisfying the query.", "items": {"$ref": "MemberRelation"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results.", "type": "string"}}, "type": "object"}, "SecuritySettings": {"description": "The definition of security settings.", "id": "SecuritySettings", "properties": {"memberRestriction": {"$ref": "MemberRestriction", "description": "The Member Restriction value"}, "name": {"description": "Output only. The resource name of the security settings. Shall be of the form `groups/{group_id}/securitySettings`.", "readOnly": true, "type": "string"}}, "type": "object"}, "SendUserInvitationRequest": {"description": "A request to send email for inviting target user corresponding to the UserInvitation.", "id": "SendUserInvitationRequest", "properties": {}, "type": "object"}, "Setting": {"description": "Setting", "id": "Setting", "properties": {"type": {"description": "Required. Immutable. The type of the Setting. .", "type": "string"}, "value": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Required. The value of the Setting.", "type": "object"}}, "type": "object"}, "SignInBehavior": {"description": "Controls sign-in behavior.", "id": "SignInBehavior", "properties": {"redirectCondition": {"description": "When to redirect sign-ins to the IdP.", "enum": ["REDIRECT_CONDITION_UNSPECIFIED", "NEVER"], "enumDescriptions": ["Default and means \"always\"", "Sign-in flows where the user is prompted for their identity will not redirect to the IdP (so the user will most likely be prompted by Google for a password), but special flows like IdP-initiated SAML and sign-in following automatic redirection to the IdP by domain-specific service URLs will accept the IdP's assertion of the user's identity."], "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TransitiveMembershipRole": {"description": "Message representing the role of a TransitiveMembership.", "id": "TransitiveMembershipRole", "properties": {"role": {"description": "TransitiveMembershipRole in string format. Currently supported TransitiveMembershipRoles: `\"MEMBER\"`, `\"OWNER\"`, and `\"MANAGER\"`.", "type": "string"}}, "type": "object"}, "UpdateGroupMetadata": {"description": "Metadata for UpdateGroup LRO.", "id": "UpdateGroupMetadata", "properties": {}, "type": "object"}, "UpdateInboundSamlSsoProfileOperationMetadata": {"description": "LRO response metadata for InboundSamlSsoProfilesService.UpdateInboundSamlSsoProfile.", "id": "UpdateInboundSamlSsoProfileOperationMetadata", "properties": {"state": {"description": "State of this Operation Will be \"awaiting-multi-party-approval\" when the operation is deferred due to the target customer having enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448).", "type": "string"}}, "type": "object"}, "UpdateInboundSsoAssignmentOperationMetadata": {"description": "LRO response metadata for InboundSsoAssignmentsService.UpdateInboundSsoAssignment.", "id": "UpdateInboundSsoAssignmentOperationMetadata", "properties": {}, "type": "object"}, "UpdateMembershipMetadata": {"description": "Metadata for UpdateMembership LRO.", "id": "UpdateMembershipMetadata", "properties": {}, "type": "object"}, "UpdateMembershipRolesParams": {"description": "The details of an update to a `MembershipRole`.", "id": "UpdateMembershipRolesParams", "properties": {"fieldMask": {"description": "The fully-qualified names of fields to update. May only contain the field `expiry_detail.expire_time`.", "format": "google-fieldmask", "type": "string"}, "membershipRole": {"$ref": "MembershipRole", "description": "The `MembershipRole`s to be updated. Only `MEMBER` `MembershipRole` can currently be updated."}}, "type": "object"}, "UserInvitation": {"description": "The `UserInvitation` resource represents an email that can be sent to an unmanaged user account inviting them to join the customer's Google Workspace or Cloud Identity account. An unmanaged account shares an email address domain with the Google Workspace or Cloud Identity account but is not managed by it yet. If the user accepts the `UserInvitation`, the user account will become managed.", "id": "UserInvitation", "properties": {"mailsSentCount": {"description": "Number of invitation emails sent to the user.", "format": "int64", "type": "string"}, "name": {"description": "Shall be of the form `customers/{customer}/userinvitations/{user_email_address}`.", "type": "string"}, "state": {"description": "State of the `UserInvitation`.", "enum": ["STATE_UNSPECIFIED", "NOT_YET_SENT", "INVITED", "ACCEPTED", "DECLINED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The `UserInvitation` has been created and is ready for sending as an email.", "The user has been invited by email.", "The user has accepted the invitation and is part of the organization.", "The user declined the invitation."], "type": "string"}, "updateTime": {"description": "Time when the `UserInvitation` was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Identity API", "version": "v1", "version_module": true}