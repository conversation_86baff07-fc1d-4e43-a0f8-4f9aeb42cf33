{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://areainsights.googleapis.com/", "batchPath": "batch", "canonicalName": "Area Insights", "description": "Places Aggregate API.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/maps/documentation/places-aggregate/overview", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "areainsights:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://areainsights.mtls.googleapis.com/", "name": "areainsights", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"v1": {"methods": {"computeInsights": {"description": "This method lets you retrieve insights about areas using a variety of filter such as: area, place type, operating status, price level and ratings. Currently \"count\" and \"places\" insights are supported. With \"count\" insights you can answer questions such as \"How many restaurant are located in California that are operational, are inexpensive and have an average rating of at least 4 stars\" (see `insight` enum for more details). With \"places\" insights, you can determine which places match the requested filter. Clients can then use those place resource names to fetch more details about each individual place using the Places API.", "flatPath": "v1:computeInsights", "httpMethod": "POST", "id": "areainsights.computeInsights", "parameterOrder": [], "parameters": {}, "path": "v1:computeInsights", "request": {"$ref": "ComputeInsightsRequest"}, "response": {"$ref": "ComputeInsightsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250622", "rootUrl": "https://areainsights.googleapis.com/", "schemas": {"Circle": {"description": "A circle is defined by a center point and radius in meters.", "id": "Circle", "properties": {"latLng": {"$ref": "LatLng", "description": "The latitude and longitude of the center of the circle."}, "place": {"description": "**Format:** Must be in the format `places/PLACE_ID`, where `PLACE_ID` is the unique identifier of a place. For example: `places/ChIJgUbEo8cfqokR5lP9_Wh_DaM`.", "type": "string"}, "radius": {"description": "Optional. The radius of the circle in meters", "format": "int32", "type": "integer"}}, "type": "object"}, "ComputeInsightsRequest": {"description": "Request for the ComputeInsights RPC.", "id": "ComputeInsightsRequest", "properties": {"filter": {"$ref": "Filter", "description": "Required. Insight filter."}, "insights": {"description": "Required. Insights to compute. Currently only INSIGHT_COUNT and INSIGHT_PLACES are supported.", "items": {"enum": ["INSIGHT_UNSPECIFIED", "INSIGHT_COUNT", "INSIGHT_PLACES"], "enumDescriptions": ["Not Specified.", "Count insight. When this insight is specified ComputeInsights returns the number of places that match the specified filter criteria. Example request: ``` { \"insights\": [\"INSIGHT_COUNT\"], \"filter\": { \"locationFilter\": { \"region\": { \"place\": \"places/ChIJPV4oX_65j4ARVW8IJ6IJUYs\" } }, \"typeFilter\": { \"includedTypes\": [\"restaurant\"] }, \"operatingStatus\": [\"OPERATING_STATUS_OPERATIONAL\"], \"priceLevels\": [ \"PRICE_LEVEL_FREE\", \"PRICE_LEVEL_INEXPENSIVE\" ], \"ratingFilter\": { \"minRating\": 4.0 } } } ``` Example response: ``` { \"count\": 1234 } ```", "Return Places When this insight is specified ComputeInsights returns places IDs that match the specified filter criteria. Example request: ``` { \"insights\": [\"INSIGHT_PLACES\"], \"filter\": { \"locationFilter\": { \"region\": { \"place\": \"places/ChIJPV4oX_65j4ARVW8IJ6IJUYs\" } }, \"typeFilter\": { \"includedTypes\": [\"restaurant\"] }, \"operatingStatus\": [\"OPERATING_STATUS_OPERATIONAL\"], \"priceLevels\": [ \"PRICE_LEVEL_FREE\", \"PRICE_LEVEL_INEXPENSIVE\" ], \"ratingFilter\": { \"minRating\": 4.0 } } } ``` Example response: ``` { \"placeInsights\": [ {\"place\": \"places/ABC\"}, {\"place\": \"places/PQR\"}, {\"place\": \"places/XYZ\"} ] } ```"], "type": "string"}, "type": "array"}}, "type": "object"}, "ComputeInsightsResponse": {"description": "Response for the ComputeInsights RPC.", "id": "ComputeInsightsResponse", "properties": {"count": {"description": "Result for Insights.INSIGHT_COUNT.", "format": "int64", "type": "string"}, "placeInsights": {"description": "Result for Insights.INSIGHT_PLACES.", "items": {"$ref": "PlaceInsight"}, "type": "array"}}, "type": "object"}, "CustomArea": {"description": "Custom Area.", "id": "CustomArea", "properties": {"polygon": {"$ref": "Polygon", "description": "Required. The custom area represented as a polygon"}}, "type": "object"}, "Filter": {"description": "Filters for the ComputeInsights RPC.", "id": "Filter", "properties": {"locationFilter": {"$ref": "LocationFilter", "description": "Required. Restricts results to places which are located in the area specified by location filters."}, "operatingStatus": {"description": "Optional. Restricts results to places whose operating status is included on this list. If operating_status is not set, OPERATING_STATUS_OPERATIONAL is used as default.", "items": {"enum": ["OPERATING_STATUS_UNSPECIFIED", "OPERATING_STATUS_OPERATIONAL", "OPERATING_STATUS_PERMANENTLY_CLOSED", "OPERATING_STATUS_TEMPORARILY_CLOSED"], "enumDescriptions": ["Not specified. This value should not be used.", "The place is operational and its open during its defined hours.", "The Place is no longer in business.", "The place is temporarily closed and expected to reopen in the future."], "type": "string"}, "type": "array"}, "priceLevels": {"description": "Optional. Restricts results to places whose price level is included on this list. If `price_levels` is not set, all price levels are included in the results.", "items": {"enum": ["PRICE_LEVEL_UNSPECIFIED", "PRICE_LEVEL_FREE", "PRICE_LEVEL_INEXPENSIVE", "PRICE_LEVEL_MODERATE", "PRICE_LEVEL_EXPENSIVE", "PRICE_LEVEL_VERY_EXPENSIVE"], "enumDescriptions": ["Not specified. This value should not be used.", "Place provides free services.", "Place provides inexpensive services.", "Place provides moderately priced services.", "Place provides expensive services.", "Place provides very expensive services."], "type": "string"}, "type": "array"}, "ratingFilter": {"$ref": "RatingFilter", "description": "Optional. Restricts results to places whose average user ratings are in the range specified by rating_filter. If rating_filter is not set, all ratings are included in the result."}, "typeFilter": {"$ref": "TypeFilter", "description": "Required. Place type filters."}}, "type": "object"}, "LatLng": {"description": "An object that represents a latitude/longitude pair. This is expressed as a pair of doubles to represent degrees latitude and degrees longitude. Unless specified otherwise, this object must conform to the WGS84 standard. Values must be within normalized ranges.", "id": "LatLng", "properties": {"latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "type": "number"}, "longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "type": "number"}}, "type": "object"}, "LocationFilter": {"description": "Location filters. Specifies the area of interest for the insight.", "id": "LocationFilter", "properties": {"circle": {"$ref": "Circle", "description": "Area as a circle."}, "customArea": {"$ref": "CustomArea", "description": "Custom area specified by a polygon."}, "region": {"$ref": "Region", "description": "Area as region."}}, "type": "object"}, "PlaceInsight": {"description": "Holds information about a place", "id": "PlaceInsight", "properties": {"place": {"description": "The unique identifier of the place. This resource name can be used to retrieve details about the place using the [Places API](https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places/get).", "type": "string"}}, "type": "object"}, "Polygon": {"description": "A polygon is represented by a series of connected coordinates in an counterclockwise ordered sequence. The coordinates form a closed loop and define a filled region. The first and last coordinates are equivalent, and they must contain identical values. The format is a simplified version of GeoJSON polygons (we only support one counterclockwise exterior ring).", "id": "Polygon", "properties": {"coordinates": {"description": "Optional. The coordinates that define the polygon.", "items": {"$ref": "LatLng"}, "type": "array"}}, "type": "object"}, "RatingFilter": {"description": "Average user rating filters.", "id": "RatingFilter", "properties": {"maxRating": {"description": "Optional. Restricts results to places whose average user rating is strictly less than or equal to max_rating. Values must be between 1.0 and 5.0.", "format": "float", "type": "number"}, "minRating": {"description": "Optional. Restricts results to places whose average user rating is greater than or equal to min_rating. Values must be between 1.0 and 5.0.", "format": "float", "type": "number"}}, "type": "object"}, "Region": {"description": "A region is a geographic boundary such as: cities, postal codes, counties, states, etc.", "id": "Region", "properties": {"place": {"description": "The [place ID](https://developers.google.com/maps/documentation/places/web-service/place-id) of the geographic region. Not all region types are supported; see documentation for details. **Format:** Must be in the format `places/PLACE_ID`, where `PLACE_ID` is the unique identifier of a place. For example: `places/ChIJPV4oX_65j4ARVW8IJ6IJUYs`.", "type": "string"}}, "type": "object"}, "TypeFilter": {"description": "Place type filters. Only Place types from [Table a](https://developers.google.com/maps/documentation/places/web-service/place-types#table-a) are supported. A place can only have a single primary type associated with it. For example, the primary type might be \"mexican_restaurant\" or \"steak_house\". Use included_primary_types and excluded_primary_types to filter the results on a place's primary type. A place can also have multiple type values associated with it. For example a restaurant might have the following types: \"seafood_restaurant\", \"restaurant\", \"food\", \"point_of_interest\", \"establishment\". Use included_types and excluded_types to filter the results on the list of types associated with a place. If a search is specified with multiple type restrictions, only places that satisfy all of the restrictions are returned. For example, if you specify {\"included_types\": [\"restaurant\"], \"excluded_primary_types\": [\"steak_house\"]}, the returned places provide \"restaurant\" related services but do not operate primarily as a \"steak_house\". If there are any conflicting types, i.e. a type appears in both included_types and excluded_types types or included_primary_types and excluded_primary_types, an INVALID_ARGUMENT error is returned. One of included_types or included_primary_types must be set.", "id": "TypeFilter", "properties": {"excludedPrimaryTypes": {"description": "Optional. Excluded primary Place types.", "items": {"type": "string"}, "type": "array"}, "excludedTypes": {"description": "Optional. Excluded Place types.", "items": {"type": "string"}, "type": "array"}, "includedPrimaryTypes": {"description": "Optional. Included primary Place types.", "items": {"type": "string"}, "type": "array"}, "includedTypes": {"description": "Optional. Included Place types.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Places Aggregate API", "version": "v1", "version_module": true}