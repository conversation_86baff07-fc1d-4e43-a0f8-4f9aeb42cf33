{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://alloydb.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud AlloyDB Admin", "description": "AlloyDB for PostgreSQL is an open source-compatible database service that provides a powerful option for migrating, modernizing, or building commercial-grade applications. It offers full compatibility with standard PostgreSQL, and is more than 4x faster for transactional workloads and up to 100x faster for analytical queries than standard PostgreSQL in our performance tests. AlloyDB for PostgreSQL offers a 99.99 percent availability SLA inclusive of maintenance. AlloyDB is optimized for the most demanding use cases, allowing you to build new applications that require high transaction throughput, large database sizes, or multiple read resources; scale existing PostgreSQL workloads with no application changes; and modernize legacy proprietary databases. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/alloydb/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "alloydb:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://alloydb.mtls.googleapis.com/", "name": "alloydb", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "alloydb.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudLocationLocation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1alpha/projects/{projectsId}/locations", "httpMethod": "GET", "id": "alloydb.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/locations", "response": {"$ref": "GoogleCloudLocationListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"backups": {"methods": {"create": {"description": "Creates a new Backup in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/backups", "httpMethod": "POST", "id": "alloydb.projects.locations.backups.create", "parameterOrder": ["parent"], "parameters": {"backupId": {"description": "Required. ID of the requesting object.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, the backend validates the request, but doesn't actually execute it.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/backups", "request": {"$ref": "Backup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Backup.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}", "httpMethod": "DELETE", "id": "alloydb.projects.locations.backups.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The current etag of the Backup. If an etag is provided and does not match the current etag of the Backup, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the resource. For the required format, see the comment on the Backup.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, the backend validates the request, but doesn't actually execute it.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Backup.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}", "httpMethod": "GET", "id": "alloydb.projects.locations.backups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Backup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Backups in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/backups", "httpMethod": "GET", "id": "alloydb.projects.locations.backups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListBackupsRequest", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/backups", "response": {"$ref": "ListBackupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Backup.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}", "httpMethod": "PATCH", "id": "alloydb.projects.locations.backups.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, update succeeds even if instance is not found. In that case, a new backup is created and `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. The name of the backup resource with the format: * projects/{project}/locations/{region}/backups/{backup_id} where the cluster and backup ID segments should satisfy the regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase letters, numbers, and dashes, starting with a letter, and ending with a letter or number. For more details see https://google.aip.dev/122. The prefix of the backup resource name is the name of the parent resource: * projects/{project}/locations/{region}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Backup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, the backend validates the request, but doesn't actually execute it.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "Backup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "clusters": {"methods": {"create": {"description": "Creates a new Cluster in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.create", "parameterOrder": ["parent"], "parameters": {"clusterId": {"description": "Required. ID of the requesting object.", "location": "query", "type": "string"}, "parent": {"description": "Required. The location of the new cluster. For the required format, see the comment on the Cluster.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/clusters", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "createsecondary": {"description": "Creates a cluster of type SECONDARY in the given location using the primary cluster as the source.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters:createsecondary", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.createsecondary", "parameterOrder": ["parent"], "parameters": {"clusterId": {"description": "Required. ID of the requesting object (the secondary cluster).", "location": "query", "type": "string"}, "parent": {"description": "Required. The location of the new cluster. For the required format, see the comment on the Cluster.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/clusters:createsecondary", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Cluster.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}", "httpMethod": "DELETE", "id": "alloydb.projects.locations.clusters.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The current etag of the Cluster. If an etag is provided and does not match the current etag of the Cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "force": {"description": "Optional. Whether to cascade delete child instances for given cluster.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the resource. For the required format, see the comment on the Cluster.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "export": {"description": "Exports data from the cluster. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:export", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.export", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the cluster.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:export", "request": {"$ref": "ExportClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Cluster.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Cluster.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. The view of the cluster to return. Returns all default fields if not set.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "CLUSTER_VIEW_BASIC", "CLUSTER_VIEW_CONTINUOUS_BACKUP"], "enumDescriptions": ["CLUSTER_VIEW_UNSPECIFIED Not specified, equivalent to BASIC.", "BASIC server responses include all the relevant cluster details, excluding Cluster.ContinuousBackupInfo.EarliestRestorableTime and other view-specific fields. The default value.", "CONTINUOUS_BACKUP response returns all the fields from BASIC plus the earliest restorable time if continuous backups are enabled. May increase latency."], "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Cluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Imports data to the cluster. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:import", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.import", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the cluster.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:import", "request": {"$ref": "ImportClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Clusters in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource. For the required format, see the comment on the Cluster.name field. Additionally, you can perform an aggregated list operation by specifying a value with the following format: * projects/{project}/locations/-", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/clusters", "response": {"$ref": "ListClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Cluster.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}", "httpMethod": "PATCH", "id": "alloydb.projects.locations.clusters.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, update succeeds even if cluster is not found. In that case, a new cluster is created and `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. The name of the cluster resource with the format: * projects/{project}/locations/{region}/clusters/{cluster_id} where the cluster ID segment should satisfy the regex expression `[a-z0-9-]+`. For more details see https://google.aip.dev/122. The prefix of the cluster resource name is the name of the parent resource: * projects/{project}/locations/{region}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Cluster resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "promote": {"description": "Promotes a SECONDARY cluster. This turns down replication from the PRIMARY cluster and promotes a secondary cluster into its own standalone cluster. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:promote", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.promote", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Cluster.name field", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:promote", "request": {"$ref": "PromoteClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restore": {"description": "Creates a new Cluster in a given project and location, with a volume restored from the provided source, either a backup ID or a point-in-time and a source cluster.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters:restore", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.restore", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the parent resource. For the required format, see the comment on the Cluster.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/clusters:restore", "request": {"$ref": "RestoreClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restoreFromCloudSQL": {"description": "Restores an AlloyDB cluster from a CloudSQL resource.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters:restoreFromCloudSQL", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.restoreFromCloudSQL", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The location of the new cluster. For the required format, see the comment on Cluster.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/clusters:restoreFromCloudSQL", "request": {"$ref": "RestoreFromCloudSQLRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "switchover": {"description": "Switches the roles of PRIMARY and SECONDARY clusters without any data loss. This promotes the SECONDARY cluster to PRIMARY and sets up the original PRIMARY cluster to replicate from this newly promoted cluster.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:switchover", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.switchover", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Cluster.name field", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:switchover", "request": {"$ref": "SwitchoverClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "upgrade": {"description": "Upgrades a single Cluster. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}:upgrade", "httpMethod": "PATCH", "id": "alloydb.projects.locations.clusters.upgrade", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the cluster.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:upgrade", "request": {"$ref": "UpgradeClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"instances": {"methods": {"create": {"description": "Creates a new Instance in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.instances.create", "parameterOrder": ["parent"], "parameters": {"instanceId": {"description": "Required. ID of the requesting object.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/instances", "request": {"$ref": "Instance"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "createsecondary": {"description": "Creates a new SECONDARY Instance in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances:createsecondary", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.instances.createsecondary", "parameterOrder": ["parent"], "parameters": {"instanceId": {"description": "Required. ID of the requesting object.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/instances:createsecondary", "request": {"$ref": "Instance"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Instance.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}", "httpMethod": "DELETE", "id": "alloydb.projects.locations.clusters.instances.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The current etag of the Instance. If an etag is provided and does not match the current etag of the Instance, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "failover": {"description": "Forces a Failover for a highly available instance. Failover promotes the HA standby instance as the new primary. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}:failover", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.instances.failover", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:failover", "request": {"$ref": "FailoverInstanceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Instance.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.instances.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The view of the instance to return.", "enum": ["INSTANCE_VIEW_UNSPECIFIED", "INSTANCE_VIEW_BASIC", "INSTANCE_VIEW_FULL"], "enumDescriptions": ["INSTANCE_VIEW_UNSPECIFIED Not specified, equivalent to BASIC.", "BASIC server responses for a primary or read instance include all the relevant instance details, excluding the details of each node in the instance. The default value.", "FULL response is equivalent to BASIC for primary instance (for now). For read pool instance, this includes details of each node in the pool."], "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Instance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getConnectionInfo": {"description": "Get instance metadata used for a connection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}/connectionInfo", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.instances.getConnectionInfo", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the parent resource. The required format is: projects/{project}/locations/{location}/clusters/{cluster}/instances/{instance}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/connectionInfo", "response": {"$ref": "ConnectionInfo"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "injectFault": {"description": "Injects fault in an instance. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}:injectFault", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.instances.injectFault", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:injectFault", "request": {"$ref": "InjectFaultRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Instances in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.instances.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource. For the required format, see the comment on the Instance.name field. Additionally, you can perform an aggregated list operation by specifying a value with one of the following formats: * projects/{project}/locations/-/clusters/- * projects/{project}/locations/{region}/clusters/-", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/instances", "response": {"$ref": "ListInstancesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Instance.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}", "httpMethod": "PATCH", "id": "alloydb.projects.locations.clusters.instances.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, update succeeds even if instance is not found. In that case, a new instance is created and `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. The name of the instance resource with the format: * projects/{project}/locations/{region}/clusters/{cluster_id}/instances/{instance_id} where the cluster and instance ID segments should satisfy the regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase letters, numbers, and dashes, starting with a letter, and ending with a letter or number. For more details see https://google.aip.dev/122. The prefix of the instance resource name is the name of the parent resource: * projects/{project}/locations/{region}/clusters/{cluster_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Instance resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "Instance"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restart": {"description": "Restart an Instance in a cluster. Imperative only.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/instances/{instancesId}:restart", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.instances.restart", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the Instance.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:restart", "request": {"$ref": "RestartInstanceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "users": {"methods": {"create": {"description": "Creates a new User in a given project, location, and cluster.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users", "httpMethod": "POST", "id": "alloydb.projects.locations.clusters.users.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "userId": {"description": "Required. ID of the requesting object.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, the backend validates the request, but doesn't actually execute it.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/users", "request": {"$ref": "User"}, "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single User.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users/{usersId}", "httpMethod": "DELETE", "id": "alloydb.projects.locations.clusters.users.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the User.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/users/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, the backend validates the request, but doesn't actually execute it.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single User.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users/{usersId}", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.users.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the resource. For the required format, see the comment on the User.name field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/users/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Users in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users", "httpMethod": "GET", "id": "alloydb.projects.locations.clusters.users.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListUsersRequest", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/users", "response": {"$ref": "ListUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single User.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/users/{usersId}", "httpMethod": "PATCH", "id": "alloydb.projects.locations.clusters.users.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. Allow missing fields in the update mask.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. Name of the resource in the form of projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/users/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the User resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, the backend validates the request, but doesn't actually execute it.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "User"}, "response": {"$ref": "User"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "alloydb.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "alloydb.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "alloydb.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "alloydb.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "supportedDatabaseFlags": {"methods": {"list": {"description": "Lists SupportedDatabaseFlags for a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/supportedDatabaseFlags", "httpMethod": "GET", "id": "alloydb.projects.locations.supportedDatabaseFlags.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent resource. The required format is: * projects/{project}/locations/{location} Regardless of the parent specified here, as long it is contains a valid project and location, the service will return a static list of supported flags resources. Note that we do not yet support region-specific flags.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "scope": {"description": "Optional. The scope for which supported flags are requested. If not specified, default is DATABASE.", "enum": ["SCOPE_UNSPECIFIED", "DATABASE", "CONNECTION_POOL"], "enumDescriptions": ["The scope of the flag is not specified. Default is DATABASE.", "The flag is a database flag.", "The flag is a connection pool flag."], "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/supportedDatabaseFlags", "response": {"$ref": "ListSupportedDatabaseFlagsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250612", "rootUrl": "https://alloydb.googleapis.com/", "schemas": {"AuthorizedNetwork": {"description": "AuthorizedNetwork contains metadata for an authorized network.", "id": "AuthorizedNetwork", "properties": {"cidrRange": {"description": "CIDR range for one authorzied network of the instance.", "type": "string"}}, "type": "object"}, "AutomatedBackupPolicy": {"description": "Message describing the user-specified automated backup policy. All fields in the automated backup policy are optional. Defaults for each field are provided if they are not set.", "id": "AutomatedBackupPolicy", "properties": {"backupWindow": {"description": "The length of the time window during which a backup can be taken. If a backup does not succeed within this time window, it will be canceled and considered failed. The backup window must be at least 5 minutes long. There is no upper bound on the window. If not set, it defaults to 1 hour.", "format": "google-duration", "type": "string"}, "enabled": {"description": "Whether automated automated backups are enabled. If not set, defaults to true.", "type": "boolean"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Optional. The encryption config can be specified to encrypt the backups with a customer-managed encryption key (CMEK). When this field is not specified, the backup will use the cluster's encryption config."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels to apply to backups created using this configuration.", "type": "object"}, "location": {"description": "The location where the backup will be stored. Currently, the only supported option is to store the backup in the same region as the cluster. If empty, defaults to the region of the cluster.", "type": "string"}, "quantityBasedRetention": {"$ref": "QuantityBasedRetention", "description": "Quantity-based Backup retention policy to retain recent backups."}, "timeBasedRetention": {"$ref": "TimeBasedRetention", "description": "Time-based Backup retention policy."}, "weeklySchedule": {"$ref": "WeeklySchedule", "description": "Weekly schedule for the Backup."}}, "type": "object"}, "Backup": {"description": "Message describing Backup object", "id": "Backup", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations to allow client tools to store small amount of arbitrary data. This is distinct from labels. https://google.aip.dev/128", "type": "object"}, "clusterName": {"description": "Required. The full resource name of the backup source cluster (e.g., projects/{project}/locations/{region}/clusters/{cluster_id}).", "type": "string"}, "clusterUid": {"description": "Output only. The system-generated UID of the cluster which was used to create this resource.", "readOnly": true, "type": "string"}, "createCompletionTime": {"description": "Output only. Timestamp when the resource finished being created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseVersion": {"description": "Output only. The database engine major version of the cluster this backup was created from. Any restored cluster created from this backup will have the same database version.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. Delete time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User-provided description of the backup.", "type": "string"}, "displayName": {"description": "User-settable and human-readable display name for the Backup.", "type": "string"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Optional. The encryption config can be specified to encrypt the backup with a customer-managed encryption key (CMEK). When this field is not specified, the backup will then use default encryption scheme to protect the user data."}, "encryptionInfo": {"$ref": "EncryptionInfo", "description": "Output only. The encryption information for the backup.", "readOnly": true}, "etag": {"description": "For Resource freshness validation (https://google.aip.dev/154)", "type": "string"}, "expiryQuantity": {"$ref": "QuantityBasedExpiry", "description": "Output only. The QuantityBasedExpiry of the backup, specified by the backup's retention policy. Once the expiry quantity is over retention, the backup is eligible to be garbage collected.", "readOnly": true}, "expiryTime": {"description": "Output only. The time at which after the backup is eligible to be garbage collected. It is the duration specified by the backup's retention policy, added to the backup's create_time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs", "type": "object"}, "name": {"description": "Output only. The name of the backup resource with the format: * projects/{project}/locations/{region}/backups/{backup_id} where the cluster and backup ID segments should satisfy the regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase letters, numbers, and dashes, starting with a letter, and ending with a letter or number. For more details see https://google.aip.dev/122. The prefix of the backup resource name is the name of the parent resource: * projects/{project}/locations/{region}", "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. Reconciling (https://google.aip.dev/128#reconciliation), if true, indicates that the service is actively updating the resource. This can happen due to user-triggered updates or system actions like failover or maintenance.", "readOnly": true, "type": "boolean"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "sizeBytes": {"description": "Output only. The size of the backup in bytes.", "format": "int64", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the backup.", "enum": ["STATE_UNSPECIFIED", "READY", "CREATING", "FAILED", "DELETING"], "enumDescriptions": ["The state of the backup is unknown.", "The backup is ready.", "The backup is creating.", "The backup failed.", "The backup is being deleted."], "readOnly": true, "type": "string"}, "tags": {"additionalProperties": {"type": "string"}, "description": "Optional. Input only. Immutable. Tag keys/values directly bound to this resource. For example: ``` \"123/environment\": \"production\", \"123/costCenter\": \"marketing\" ```", "type": "object"}, "type": {"description": "The backup type, which suggests the trigger for the backup.", "enum": ["TYPE_UNSPECIFIED", "ON_DEMAND", "AUTOMATED", "CONTINUOUS"], "enumDescriptions": ["Backup Type is unknown.", "ON_DEMAND backups that were triggered by the customer (e.g., not AUTOMATED).", "AUTOMATED backups triggered by the automated backups scheduler pursuant to an automated backup policy.", "CONTINUOUS backups triggered by the automated backups scheduler due to a continuous backup policy."], "type": "string"}, "uid": {"description": "Output only. The system-generated UID of the resource. The UID is assigned when the resource is created, and it is retained until it is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time stamp Users should not infer any meaning from this field. Its value is generally unrelated to the timing of the backup creation operation.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupSource": {"description": "Message describing a BackupSource.", "id": "BackupSource", "properties": {"backupName": {"description": "Required. The name of the backup resource with the format: * projects/{project}/locations/{region}/backups/{backup_id}", "type": "string"}, "backupUid": {"description": "Output only. The system-generated UID of the backup which was used to create this resource. The UID is generated when the backup is created, and it is retained until the backup is deleted.", "readOnly": true, "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "ClientConnectionConfig": {"description": "Client connection configuration", "id": "ClientConnectionConfig", "properties": {"requireConnectors": {"description": "Optional. Configuration to enforce connectors only (ex: AuthProxy) connections to the database.", "type": "boolean"}, "sslConfig": {"$ref": "SslConfig", "description": "Optional. SSL configuration option for this instance."}}, "type": "object"}, "CloudControl2SharedOperationsReconciliationOperationMetadata": {"description": "Operation metadata returned by the CLH during resource state reconciliation.", "id": "CloudControl2SharedOperationsReconciliationOperationMetadata", "properties": {"deleteResource": {"deprecated": true, "description": "DEPRECATED. Use exclusive_action instead.", "type": "boolean"}, "exclusiveAction": {"description": "Excluisive action returned by the CLH.", "enum": ["UNKNOWN_REPAIR_ACTION", "DELETE", "RETRY"], "enumDeprecated": [false, true, false], "enumDescriptions": ["Unknown repair action.", "The resource has to be deleted. When using this bit, the CLH should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE OperationSignal in SideChannel.", "This resource could not be repaired but the repair should be tried again at a later time. This can happen if there is a dependency that needs to be resolved first- e.g. if a parent resource must be repaired before a child resource."], "type": "string"}}, "type": "object"}, "CloudSQLBackupRunSource": {"description": "The source CloudSQL backup resource.", "id": "CloudSQLBackupRunSource", "properties": {"backupRunId": {"description": "Required. The CloudSQL backup run ID.", "format": "int64", "type": "string"}, "instanceId": {"description": "Required. The CloudSQL instance ID.", "type": "string"}, "project": {"description": "The project ID of the source CloudSQL instance. This should be the same as the AlloyDB cluster's project.", "type": "string"}}, "type": "object"}, "Cluster": {"description": "A cluster is a collection of regional AlloyDB resources. It can include a primary instance and one or more read pool instances. All cluster resources share a storage layer, which scales as needed.", "id": "Cluster", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations to allow client tools to store small amount of arbitrary data. This is distinct from labels. https://google.aip.dev/128", "type": "object"}, "automatedBackupPolicy": {"$ref": "AutomatedBackupPolicy", "description": "The automated backup policy for this cluster. If no policy is provided then the default policy will be used. If backups are supported for the cluster, the default policy takes one backup a day, has a backup window of 1 hour, and retains backups for 14 days. For more information on the defaults, consult the documentation for the message type."}, "backupSource": {"$ref": "BackupSource", "description": "Output only. Cluster created from backup.", "readOnly": true}, "cloudsqlBackupRunSource": {"$ref": "CloudSQLBackupRunSource", "description": "Output only. Cluster created from CloudSQL snapshot.", "readOnly": true}, "clusterType": {"description": "Output only. The type of the cluster. This is an output-only field and it's populated at the Cluster creation time or the Cluster promotion time. The cluster type is determined by which RPC was used to create the cluster (i.e. `CreateCluster` vs. `CreateSecondaryCluster`", "enum": ["CLUSTER_TYPE_UNSPECIFIED", "PRIMARY", "SECONDARY"], "enumDescriptions": ["The type of the cluster is unknown.", "Primary cluster that support read and write operations.", "Secondary cluster that is replicating from another region. This only supports read."], "readOnly": true, "type": "string"}, "continuousBackupConfig": {"$ref": "ContinuousBackupConfig", "description": "Optional. Continuous backup configuration for this cluster."}, "continuousBackupInfo": {"$ref": "ContinuousBackupInfo", "description": "Output only. Continuous backup properties for this cluster.", "readOnly": true}, "createTime": {"description": "Output only. Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseVersion": {"description": "Optional. The database engine major version. This is an optional field and it is populated at the Cluster creation time. If a database version is not supplied at cluster creation time, then a default database version will be used.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "type": "string"}, "deleteTime": {"description": "Output only. Delete time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "User-settable and human-readable display name for the Cluster.", "type": "string"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Optional. The encryption config can be specified to encrypt the data disks and other persistent data resources of a cluster with a customer-managed encryption key (CMEK). When this field is not specified, the cluster will then use default encryption scheme to protect the user data."}, "encryptionInfo": {"$ref": "EncryptionInfo", "description": "Output only. The encryption information for the cluster.", "readOnly": true}, "etag": {"description": "For Resource freshness validation (https://google.aip.dev/154)", "type": "string"}, "geminiConfig": {"$ref": "GeminiClusterConfig", "deprecated": true, "description": "Optional. Deprecated and unused. This field will be removed in the near future."}, "initialUser": {"$ref": "UserPassword", "description": "Input only. Initial user to setup during cluster creation. Required. If used in `RestoreCluster` this is ignored."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs", "type": "object"}, "maintenanceSchedule": {"$ref": "MaintenanceSchedule", "description": "Output only. The maintenance schedule for the cluster, generated for a specific rollout if a maintenance window is set.", "readOnly": true}, "maintenanceUpdatePolicy": {"$ref": "MaintenanceUpdatePolicy", "description": "Optional. The maintenance update policy determines when to allow or deny updates."}, "migrationSource": {"$ref": "MigrationSource", "description": "Output only. Cluster created via DMS migration.", "readOnly": true}, "name": {"description": "Output only. The name of the cluster resource with the format: * projects/{project}/locations/{region}/clusters/{cluster_id} where the cluster ID segment should satisfy the regex expression `[a-z0-9-]+`. For more details see https://google.aip.dev/122. The prefix of the cluster resource name is the name of the parent resource: * projects/{project}/locations/{region}", "readOnly": true, "type": "string"}, "network": {"deprecated": true, "description": "Required. The resource link for the VPC network in which cluster resources are created and from which they are accessible via Private IP. The network must belong to the same project as the cluster. It is specified in the form: `projects/{project}/global/networks/{network_id}`. This is required to create a cluster. Deprecated, use network_config.network instead.", "type": "string"}, "networkConfig": {"$ref": "NetworkConfig"}, "primaryConfig": {"$ref": "PrimaryConfig", "description": "Output only. Cross Region replication config specific to PRIMARY cluster.", "readOnly": true}, "pscConfig": {"$ref": "PscConfig", "description": "Optional. The configuration for Private Service Connect (PSC) for the cluster."}, "reconciling": {"description": "Output only. Reconciling (https://google.aip.dev/128#reconciliation). Set to true if the current state of Cluster does not match the user's intended state, and the service is actively updating the resource to reconcile them. This can happen due to user-triggered updates or system actions like failover or maintenance.", "readOnly": true, "type": "boolean"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "secondaryConfig": {"$ref": "SecondaryConfig", "description": "Cross Region replication config specific to SECONDARY cluster."}, "serviceAccountEmail": {"description": "Output only. AlloyDB per-cluster service account. This service account is created per-cluster per-project, and is different from the per-project service account. The per-cluster service account naming format is subject to change.", "readOnly": true, "type": "string"}, "sslConfig": {"$ref": "SslConfig", "deprecated": true, "description": "SSL configuration for this AlloyDB cluster."}, "state": {"description": "Output only. The current serving state of the cluster.", "enum": ["STATE_UNSPECIFIED", "READY", "STOPPED", "EMPTY", "CREATING", "DELETING", "FAILED", "BOOTSTRAPPING", "MAINTENANCE", "PROMOTING"], "enumDescriptions": ["The state of the cluster is unknown.", "The cluster is active and running.", "This is unused. Even when all instances in the cluster are stopped, the cluster remains in READY state.", "The cluster is empty and has no associated resources. All instances, associated storage and backups have been deleted.", "The cluster is being created.", "The cluster is being deleted.", "The creation of the cluster failed.", "The cluster is bootstrapping with data from some other source. Direct mutations to the cluster (e.g. adding read pool) are not allowed.", "The cluster is under maintenance. AlloyDB regularly performs maintenance and upgrades on customer clusters. Updates on the cluster are not allowed while the cluster is in this state.", "The cluster is being promoted."], "readOnly": true, "type": "string"}, "subscriptionType": {"description": "Optional. Subscription type of the cluster.", "enum": ["SUBSCRIPTION_TYPE_UNSPECIFIED", "STANDARD", "TRIAL"], "enumDescriptions": ["This is an unknown subscription type. By default, the subscription type is STANDARD.", "Standard subscription.", "Trial subscription."], "type": "string"}, "tags": {"additionalProperties": {"type": "string"}, "description": "Optional. Input only. Immutable. Tag keys/values directly bound to this resource. For example: ``` \"123/environment\": \"production\", \"123/costCenter\": \"marketing\" ```", "type": "object"}, "trialMetadata": {"$ref": "TrialMetadata", "description": "Output only. Metadata for free trial clusters", "readOnly": true}, "uid": {"description": "Output only. The system-generated UID of the resource. The UID is assigned when the resource is created, and it is retained until it is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ClusterUpgradeDetails": {"description": "Upgrade details of a cluster. This cluster can be primary or secondary.", "id": "ClusterUpgradeDetails", "properties": {"clusterType": {"description": "Cluster type which can either be primary or secondary.", "enum": ["CLUSTER_TYPE_UNSPECIFIED", "PRIMARY", "SECONDARY"], "enumDescriptions": ["The type of the cluster is unknown.", "Primary cluster that support read and write operations.", "Secondary cluster that is replicating from another region. This only supports read."], "type": "string"}, "databaseVersion": {"description": "Database version of the cluster after the upgrade operation. This will be the target version if the upgrade was successful otherwise it remains the same as that before the upgrade operation.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "type": "string"}, "instanceUpgradeDetails": {"description": "Upgrade details of the instances directly associated with this cluster.", "items": {"$ref": "InstanceUpgradeDetails"}, "type": "array"}, "name": {"description": "Normalized name of the cluster", "type": "string"}, "stageInfo": {"description": "Array containing stage info associated with this cluster.", "items": {"$ref": "StageInfo"}, "type": "array"}, "upgradeStatus": {"description": "Upgrade status of the cluster.", "enum": ["STATUS_UNSPECIFIED", "NOT_STARTED", "IN_PROGRESS", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "CANCEL_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Unspecified status.", "Not started.", "In progress.", "Operation succeeded.", "Operation failed.", "Operation partially succeeded.", "Cancel is in progress.", "Cancellation complete."], "type": "string"}}, "type": "object"}, "ConnectionInfo": {"description": "ConnectionInfo singleton resource. https://google.aip.dev/156", "id": "ConnectionInfo", "properties": {"instanceUid": {"description": "Output only. The unique ID of the Instance.", "readOnly": true, "type": "string"}, "ipAddress": {"description": "Output only. The private network IP address for the Instance. This is the default IP for the instance and is always created (even if enable_public_ip is set). This is the connection endpoint for an end-user application.", "readOnly": true, "type": "string"}, "name": {"description": "The name of the ConnectionInfo singleton resource, e.g.: projects/{project}/locations/{location}/clusters/*/instances/*/connectionInfo This field currently has no semantic meaning.", "type": "string"}, "pemCertificateChain": {"deprecated": true, "description": "Output only. The pem-encoded chain that may be used to verify the X.509 certificate. Expected to be in issuer-to-root order according to RFC 5246.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "pscDnsName": {"description": "Output only. The DNS name to use with PSC for the Instance.", "readOnly": true, "type": "string"}, "publicIpAddress": {"description": "Output only. The public IP addresses for the Instance. This is available ONLY when enable_public_ip is set. This is the connection endpoint for an end-user application.", "readOnly": true, "type": "string"}}, "type": "object"}, "ConnectionPoolConfig": {"description": "Configuration for Managed Connection Pool (MCP).", "id": "ConnectionPoolConfig", "properties": {"defaultPoolSize": {"description": "Optional. Deprecated. Use 'flags' instead. The default pool size. Defaults to 20. Note: This field should not be added to client libraries if not present already.", "type": "string"}, "enable": {"description": "Optional. Deprecated; Prefer 'enabled' as this will be removed soon.", "type": "boolean"}, "enabled": {"description": "Optional. Whether to enable Managed Connection Pool (MCP).", "type": "boolean"}, "flags": {"additionalProperties": {"type": "string"}, "description": "Optional. Connection Pool flags, as a list of \"key\": \"value\" pairs.", "type": "object"}, "ignoreStartupParameters": {"description": "Optional. Deprecated. Use 'flags' instead. The list of startup parameters to ignore. Defaults to [\"extra_float_digits\"] Note: This field should not be added to client libraries if not present already.", "items": {"type": "string"}, "type": "array"}, "maxClientConn": {"description": "Optional. Deprecated. Use 'flags' instead. The maximum number of client connections allowed. Note: This field should not be added to client libraries if not present already.", "type": "string"}, "maxPreparedStatements": {"description": "Optional. Deprecated. Use 'flags' instead. The maximum number of prepared statements allowed. MCP makes sure that any statement prepared by a client, up to this limit, is available on the backing server connection in transaction and statement pooling mode. Even if the statement was originally prepared on another server connection. Defaults to 0. Note: This field should not be added to client libraries if not present already.", "type": "string"}, "minPoolSize": {"description": "Optional. Deprecated. Use 'flags' instead. The minimum pool size. Defaults to 0. Note: This field should not be added to client libraries if not present already.", "type": "string"}, "poolMode": {"description": "Optional. Deprecated. Use 'flags' instead. The pool mode. Defaults to `POOL_MODE_TRANSACTION`. Note: This field should not be added to client libraries if not present already.", "enum": ["POOL_MODE_UNSPECIFIED", "POOL_MODE_SESSION", "POOL_MODE_TRANSACTION"], "enumDescriptions": ["The pool mode is not specified. Defaults to `POOL_MODE_TRANSACTION`.", "Server is released back to pool after a client disconnects.", "Server is released back to pool after a transaction finishes."], "type": "string"}, "poolerCount": {"description": "Output only. The number of running poolers per instance.", "format": "int32", "readOnly": true, "type": "integer"}, "queryWaitTimeout": {"description": "Optional. Deprecated. Use 'flags' instead. The maximum number of seconds queries are allowed to spend waiting for execution. If the query is not assigned to a server during that time, the client is disconnected. 0 disables. Note: This field should not be added to client libraries if not present already.", "type": "string"}, "serverIdleTimeout": {"description": "Optional. Deprecated. Use 'flags' instead. The maximum number of seconds a server is allowed to be idle before it is disconnected. 0 disables. Note: This field should not be added to client libraries if not present already.", "type": "string"}, "statsUsers": {"description": "Optional. Deprecated. Use 'flags' instead. The list of users that are allowed to connect to the MCP stats console. The users must exist in the database. Note: This field should not be added to client libraries if not present already.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ContinuousBackupConfig": {"description": "ContinuousBackupConfig describes the continuous backups recovery configurations of a cluster.", "id": "ContinuousBackupConfig", "properties": {"enabled": {"description": "Whether ContinuousBackup is enabled.", "type": "boolean"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "The encryption config can be specified to encrypt the backups with a customer-managed encryption key (CMEK). When this field is not specified, the backup will use the cluster's encryption config."}, "recoveryWindowDays": {"description": "The number of days that are eligible to restore from using PITR. To support the entire recovery window, backups and logs are retained for one day more than the recovery window. If not set, defaults to 14 days.", "format": "int32", "type": "integer"}}, "type": "object"}, "ContinuousBackupInfo": {"description": "ContinuousBackupInfo describes the continuous backup properties of a cluster.", "id": "ContinuousBackupInfo", "properties": {"earliestRestorableTime": {"description": "Output only. The earliest restorable time that can be restored to. If continuous backups and recovery was recently enabled, the earliest restorable time is the creation time of the earliest eligible backup within this cluster's continuous backup recovery window. After a cluster has had continuous backups enabled for the duration of its recovery window, the earliest restorable time becomes \"now minus the recovery window\". For example, assuming a point in time recovery is attempted at 04/16/2025 3:23:00PM with a 14d recovery window, the earliest restorable time would be 04/02/2025 3:23:00PM. This field is only visible if the CLUSTER_VIEW_CONTINUOUS_BACKUP cluster view is provided.", "format": "google-datetime", "readOnly": true, "type": "string"}, "enabledTime": {"description": "Output only. When ContinuousBackup was most recently enabled. Set to null if ContinuousBackup is not enabled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "encryptionInfo": {"$ref": "EncryptionInfo", "description": "Output only. The encryption information for the WALs and backups required for ContinuousBackup.", "readOnly": true}, "schedule": {"description": "Output only. Days of the week on which a continuous backup is taken.", "items": {"enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ContinuousBackupSource": {"description": "Message describing a ContinuousBackupSource.", "id": "ContinuousBackupSource", "properties": {"cluster": {"description": "Required. The source cluster from which to restore. This cluster must have continuous backup enabled for this operation to succeed. For the required format, see the comment on the Cluster.name field.", "type": "string"}, "pointInTime": {"description": "Required. The point in time to restore to.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CsvExportOptions": {"description": "Options for exporting data in CSV format.", "id": "CsvExportOptions", "properties": {"escapeCharacter": {"description": "Optional. Specifies the character that should appear before a data character that needs to be escaped. The default is the same as quote character. The value of this argument has to be a character in Hex ASCII Code.", "type": "string"}, "fieldDelimiter": {"description": "Optional. Specifies the character that separates columns within each row (line) of the file. The default is comma. The value of this argument has to be a character in Hex ASCII Code.", "type": "string"}, "quoteCharacter": {"description": "Optional. Specifies the quoting character to be used when a data value is quoted. The default is double-quote. The value of this argument has to be a character in Hex ASCII Code.", "type": "string"}, "selectQuery": {"description": "Required. The SELECT query used to extract the data.", "type": "string"}}, "type": "object"}, "CsvImportOptions": {"description": "Options for importing data in CSV format.", "id": "CsvImportOptions", "properties": {"columns": {"description": "Optional. The columns to which CSV data is imported. If not specified, all columns of the database table are loaded with CSV data.", "items": {"type": "string"}, "type": "array"}, "escapeCharacter": {"description": "Optional. Specifies the character that should appear before a data character that needs to be escaped. The default is same as quote character. The value of this argument has to be a character in Hex ASCII Code.", "type": "string"}, "fieldDelimiter": {"description": "Optional. Specifies the character that separates columns within each row (line) of the file. The default is comma. The value of this argument has to be a character in Hex ASCII Code.", "type": "string"}, "quoteCharacter": {"description": "Optional. Specifies the quoting character to be used when a data value is quoted. The default is double-quote. The value of this argument has to be a character in Hex ASCII Code.", "type": "string"}, "table": {"description": "Required. The database table to import CSV file into.", "type": "string"}}, "type": "object"}, "DenyMaintenancePeriod": {"description": "DenyMaintenancePeriod definition. Excepting emergencies, maintenance will not be scheduled to start within this deny period. The start_date must be less than the end_date.", "id": "DenyMaintenancePeriod", "properties": {"endDate": {"$ref": "GoogleTypeDate", "description": "Deny period end date. This can be: * A full date, with non-zero year, month and day values OR * A month and day value, with a zero year for recurring"}, "startDate": {"$ref": "GoogleTypeDate", "description": "Deny period start date. This can be: * A full date, with non-zero year, month and day values OR * A month and day value, with a zero year for recurring"}, "time": {"$ref": "GoogleTypeTimeOfDay", "description": "Time in UTC when the deny period starts on start_date and ends on end_date. This can be: * Full time OR * All zeros for 00:00:00 UTC"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EncryptionConfig": {"description": "EncryptionConfig describes the encryption config of a cluster or a backup that is encrypted with a CMEK (customer-managed encryption key).", "id": "EncryptionConfig", "properties": {"kmsKeyName": {"description": "The fully-qualified resource name of the KMS key. Each Cloud KMS key is regionalized and has the following format: projects/[PROJECT]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]", "type": "string"}}, "type": "object"}, "EncryptionInfo": {"description": "EncryptionInfo describes the encryption information of a cluster or a backup.", "id": "EncryptionInfo", "properties": {"encryptionType": {"description": "Output only. Type of encryption.", "enum": ["TYPE_UNSPECIFIED", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Encryption type not specified. Defaults to GOOGLE_DEFAULT_ENCRYPTION.", "The data is encrypted at rest with a key that is fully managed by Google. No key version will be populated. This is the default state.", "The data is encrypted at rest with a key that is managed by the customer. KMS key versions will be populated."], "readOnly": true, "type": "string"}, "kmsKeyVersions": {"description": "Output only. Cloud KMS key versions that are being used to protect the database or the backup.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ExportClusterRequest": {"description": "Export cluster request.", "id": "ExportClusterRequest", "properties": {"csvExportOptions": {"$ref": "CsvExportOptions", "description": "Options for exporting data in CSV format. Required field to be set for CSV file type."}, "database": {"description": "Required. Name of the database where the export command will be executed. Note - Value provided should be the same as expected from `SELECT current_database();` and NOT as a resource reference.", "type": "string"}, "gcsDestination": {"$ref": "GcsDestination", "description": "Required. Option to export data to cloud storage."}, "sqlExportOptions": {"$ref": "SqlExportOptions", "description": "Options for exporting data in SQL format. Required field to be set for SQL file type."}}, "type": "object"}, "FailoverInstanceRequest": {"description": "Message for triggering failover on an Instance", "id": "FailoverInstanceRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}}, "type": "object"}, "GCAInstanceConfig": {"description": "Instance level configuration parameters related to the Gemini Cloud Assist product.", "id": "GCAInstanceConfig", "properties": {"gcaEntitlement": {"description": "Output only. Represents the GCA entitlement state of the instance.", "enum": ["GCA_ENTITLEMENT_TYPE_UNSPECIFIED", "GCA_STANDARD"], "enumDescriptions": ["No GCA entitlement is assigned.", "The resource is entitled to the GCA Standard Tier."], "readOnly": true, "type": "string"}}, "type": "object"}, "GcsDestination": {"description": "Destination for Export. Export will be done to cloud storage.", "id": "GcsDestination", "properties": {"uri": {"description": "Required. The path to the file in Google Cloud Storage where the export will be stored. The URI is in the form `gs://bucketName/fileName`.", "type": "string"}}, "type": "object"}, "GeminiClusterConfig": {"description": "Deprecated and unused. This message will be removed in the near future.", "id": "GeminiClusterConfig", "properties": {"entitled": {"deprecated": true, "description": "Output only. Deprecated and unused. This field will be removed in the near future.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GeminiInstanceConfig": {"description": "Deprecated and unused. This message will be removed in the near future.", "id": "GeminiInstanceConfig", "properties": {"entitled": {"deprecated": true, "description": "Output only. Deprecated and unused. This field will be removed in the near future.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GoogleCloudLocationListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "GoogleCloudLocationListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "GoogleCloudLocationLocation"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "GoogleCloudLocationLocation": {"description": "A resource that represents a Google Cloud location.", "id": "GoogleCloudLocationLocation", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleTypeTimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "GoogleTypeTimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "ImportClusterRequest": {"description": "Import cluster request.", "id": "ImportClusterRequest", "properties": {"csvImportOptions": {"$ref": "CsvImportOptions", "description": "Options for importing data in CSV format."}, "database": {"description": "Optional. Name of the database to which the import will be done. For import from SQL file, this is required only if the file does not specify a database. Note - Value provided should be the same as expected from `SELECT current_database();` and NOT as a resource reference.", "type": "string"}, "gcsUri": {"description": "Required. The path to the file in Google Cloud Storage where the source file for import will be stored. The URI is in the form `gs://bucketName/fileName`.", "type": "string"}, "sqlImportOptions": {"$ref": "SqlImportOptions", "description": "Options for importing data in SQL format."}, "user": {"description": "Optional. Database user to be used for importing the data. Note - Value provided should be the same as expected from `SELECT current_user;` and NOT as a resource reference.", "type": "string"}}, "type": "object"}, "InjectFaultRequest": {"description": "Message for triggering fault injection on an instance", "id": "InjectFaultRequest", "properties": {"faultType": {"description": "Required. The type of fault to be injected in an instance.", "enum": ["FAULT_TYPE_UNSPECIFIED", "STOP_VM"], "enumDescriptions": ["The fault type is unknown.", "Stop the VM"], "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}}, "type": "object"}, "Instance": {"description": "An Instance is a computing unit that an end customer can connect to. It's the main unit of computing resources in AlloyDB.", "id": "Instance", "properties": {"activationPolicy": {"description": "Optional. Specifies whether an instance needs to spin up. Once the instance is active, the activation policy can be updated to the `NEVER` to stop the instance. Likewise, the activation policy can be updated to `ALWAYS` to start the instance. There are restrictions around when an instance can/cannot be activated (for example, a read pool instance should be stopped before stopping primary etc.). Please refer to the API documentation for more details.", "enum": ["ACTIVATION_POLICY_UNSPECIFIED", "ALWAYS", "NEVER"], "enumDescriptions": ["The policy is not specified.", "The instance is running.", "The instance is not running."], "type": "string"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations to allow client tools to store small amount of arbitrary data. This is distinct from labels. https://google.aip.dev/128", "type": "object"}, "availabilityType": {"description": "Availability type of an Instance. If empty, defaults to REGIONAL for primary instances. For read pools, availability_type is always UNSPECIFIED. Instances in the read pools are evenly distributed across available zones within the region (i.e. read pools with more than one node will have a node in at least two zones).", "enum": ["AVAILABILITY_TYPE_UNSPECIFIED", "ZONAL", "REGIONAL"], "enumDescriptions": ["This is an unknown Availability type.", "Zonal available instance.", "Regional (or Highly) available instance."], "type": "string"}, "clientConnectionConfig": {"$ref": "ClientConnectionConfig", "description": "Optional. Client connection specific configurations"}, "connectionPoolConfig": {"$ref": "ConnectionPoolConfig", "description": "Optional. The configuration for Managed Connection Pool (MCP)."}, "createTime": {"description": "Output only. Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseFlags": {"additionalProperties": {"type": "string"}, "description": "Database flags. Set at the instance level. They are copied from the primary instance on secondary instance creation. Flags that have restrictions default to the value at primary instance on read instances during creation. Read instances can set new flags or override existing flags that are relevant for reads, for example, for enabling columnar cache on a read instance. Flags set on read instance might or might not be present on the primary instance. This is a list of \"key\": \"value\" pairs. \"key\": The name of the flag. These flags are passed at instance setup time, so include both server options and system variables for Postgres. Flags are specified with underscores, not hyphens. \"value\": The value of the flag. Booleans are set to **on** for true and **off** for false. This field must be omitted if the flag doesn't take a value.", "type": "object"}, "deleteTime": {"description": "Output only. Delete time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "User-settable and human-readable display name for the Instance.", "type": "string"}, "etag": {"description": "For Resource freshness validation (https://google.aip.dev/154)", "type": "string"}, "gcaConfig": {"$ref": "GCAInstanceConfig", "description": "Output only. Configuration parameters related to Gemini Cloud Assist.", "readOnly": true}, "gceZone": {"description": "The Compute Engine zone that the instance should serve from, per https://cloud.google.com/compute/docs/regions-zones This can ONLY be specified for ZONAL instances. If present for a REGIONAL instance, an error will be thrown. If this is absent for a ZONAL instance, instance is created in a random zone with available capacity.", "type": "string"}, "geminiConfig": {"$ref": "GeminiInstanceConfig", "deprecated": true, "description": "Optional. Deprecated and unused. This field will be removed in the near future."}, "instanceType": {"description": "Required. The type of the instance. Specified at creation time.", "enum": ["INSTANCE_TYPE_UNSPECIFIED", "PRIMARY", "READ_POOL", "SECONDARY"], "enumDescriptions": ["The type of the instance is unknown.", "PRIMARY instances support read and write operations.", "READ POOL instances support read operations only. Each read pool instance consists of one or more homogeneous nodes. * Read pool of size 1 can only have zonal availability. * Read pools with node count of 2 or more can have regional availability (nodes are present in 2 or more zones in a region).", "SECONDARY instances support read operations only. SECONDARY instance is a cross-region read replica"], "type": "string"}, "ipAddress": {"description": "Output only. The IP address for the Instance. This is the connection endpoint for an end-user application.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs", "type": "object"}, "machineConfig": {"$ref": "MachineConfig", "description": "Configurations for the machines that host the underlying database engine."}, "name": {"description": "Output only. The name of the instance resource with the format: * projects/{project}/locations/{region}/clusters/{cluster_id}/instances/{instance_id} where the cluster and instance ID segments should satisfy the regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase letters, numbers, and dashes, starting with a letter, and ending with a letter or number. For more details see https://google.aip.dev/122. The prefix of the instance resource name is the name of the parent resource: * projects/{project}/locations/{region}/clusters/{cluster_id}", "readOnly": true, "type": "string"}, "networkConfig": {"$ref": "InstanceNetworkConfig", "description": "Optional. Instance-level network configuration."}, "nodes": {"description": "Output only. List of available read-only VMs in this instance, including the standby for a PRIMARY instance.", "items": {"$ref": "Node"}, "readOnly": true, "type": "array"}, "observabilityConfig": {"$ref": "ObservabilityInstanceConfig", "description": "Configuration for observability."}, "outboundPublicIpAddresses": {"description": "Output only. All outbound public IP addresses configured for the instance.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "pscInstanceConfig": {"$ref": "PscInstanceConfig", "description": "Optional. The configuration for Private Service Connect (PSC) for the instance."}, "publicIpAddress": {"description": "Output only. The public IP addresses for the Instance. This is available ONLY when enable_public_ip is set. This is the connection endpoint for an end-user application.", "readOnly": true, "type": "string"}, "queryInsightsConfig": {"$ref": "QueryInsightsInstanceConfig", "description": "Configuration for query insights."}, "readPoolConfig": {"$ref": "ReadPoolConfig", "description": "Read pool instance configuration. This is required if the value of instanceType is READ_POOL."}, "reconciling": {"description": "Output only. Reconciling (https://google.aip.dev/128#reconciliation). Set to true if the current state of Instance does not match the user's intended state, and the service is actively updating the resource to reconcile them. This can happen due to user-triggered updates or system actions like failover or maintenance.", "readOnly": true, "type": "boolean"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current serving state of the instance.", "enum": ["STATE_UNSPECIFIED", "READY", "STOPPED", "CREATING", "DELETING", "MAINTENANCE", "FAILED", "BOOTSTRAPPING", "PROMOTING"], "enumDescriptions": ["The state of the instance is unknown.", "The instance is active and running.", "The instance is stopped. Instance name and IP resources are preserved.", "The instance is being created.", "The instance is being deleted.", "The instance is down for maintenance.", "The creation of the instance failed or a fatal error occurred during an operation on the instance. Note: Instances in this state would tried to be auto-repaired. And Customers should be able to restart, update or delete these instances.", "Index 7 is used in the producer apis for ROLLED_BACK state. Keeping that index unused in case that state also needs to exposed via consumer apis in future. The instance has been configured to sync data from some other source.", "The instance is being promoted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The system-generated UID of the resource. The UID is assigned when the resource is created, and it is retained until it is deleted.", "readOnly": true, "type": "string"}, "updatePolicy": {"$ref": "UpdatePolicy", "description": "Update policy that will be applied during instance update. This field is not persisted when you update the instance. To use a non-default update policy, you must specify explicitly specify the value in each update request."}, "updateTime": {"description": "Output only. Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "writableNode": {"$ref": "Node", "description": "Output only. This is set for the read-write VM of the PRIMARY instance only.", "readOnly": true}}, "type": "object"}, "InstanceNetworkConfig": {"description": "Metadata related to instance-level network configuration.", "id": "InstanceNetworkConfig", "properties": {"allocatedIpRangeOverride": {"description": "Optional. Name of the allocated IP range for the private IP AlloyDB instance, for example: \"google-managed-services-default\". If set, the instance IPs will be created from this allocated range and will override the IP range used by the parent cluster. The range name must comply with [RFC 1035](http://datatracker.ietf.org/doc/html/rfc1035). Specifically, the name must be 1-63 characters long and match the regular expression [a-z]([-a-z0-9]*[a-z0-9])?.", "type": "string"}, "authorizedExternalNetworks": {"description": "Optional. A list of external network authorized to access this instance.", "items": {"$ref": "AuthorizedNetwork"}, "type": "array"}, "enableOutboundPublicIp": {"description": "Optional. Enabling an outbound public IP address to support a database server sending requests out into the internet.", "type": "boolean"}, "enablePublicIp": {"description": "Optional. Enabling public ip for the instance.", "type": "boolean"}, "network": {"description": "Output only. The resource link for the VPC network in which instance resources are created and from which they are accessible via Private IP. This will be the same value as the parent cluster's network. It is specified in the form: // `projects/{project_number}/global/networks/{network_id}`.", "readOnly": true, "type": "string"}}, "type": "object"}, "InstanceUpgradeDetails": {"description": "Details regarding the upgrade of instances associated with a cluster.", "id": "InstanceUpgradeDetails", "properties": {"instanceType": {"description": "Instance type.", "enum": ["INSTANCE_TYPE_UNSPECIFIED", "PRIMARY", "READ_POOL", "SECONDARY"], "enumDescriptions": ["The type of the instance is unknown.", "PRIMARY instances support read and write operations.", "READ POOL instances support read operations only. Each read pool instance consists of one or more homogeneous nodes. * Read pool of size 1 can only have zonal availability. * Read pools with node count of 2 or more can have regional availability (nodes are present in 2 or more zones in a region).", "SECONDARY instances support read operations only. SECONDARY instance is a cross-region read replica"], "type": "string"}, "name": {"description": "Normalized name of the instance.", "type": "string"}, "upgradeStatus": {"description": "Upgrade status of the instance.", "enum": ["STATUS_UNSPECIFIED", "NOT_STARTED", "IN_PROGRESS", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "CANCEL_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Unspecified status.", "Not started.", "In progress.", "Operation succeeded.", "Operation failed.", "Operation partially succeeded.", "Cancel is in progress.", "Cancellation complete."], "type": "string"}}, "type": "object"}, "IntegerRestrictions": {"description": "Restrictions on INTEGER type values.", "id": "IntegerRestrictions", "properties": {"maxValue": {"description": "The maximum value that can be specified, if applicable.", "format": "int64", "type": "string"}, "minValue": {"description": "The minimum value that can be specified, if applicable.", "format": "int64", "type": "string"}}, "type": "object"}, "ListBackupsResponse": {"description": "Message for response to listing Backups", "id": "ListBackupsResponse", "properties": {"backups": {"description": "The list of Backup", "items": {"$ref": "Backup"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListClustersResponse": {"description": "Message for response to listing Clusters", "id": "ListClustersResponse", "properties": {"clusters": {"description": "The list of Cluster", "items": {"$ref": "Cluster"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListInstancesResponse": {"description": "Message for response to listing Instances", "id": "ListInstancesResponse", "properties": {"instances": {"description": "The list of Instance", "items": {"$ref": "Instance"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListSupportedDatabaseFlagsResponse": {"description": "Message for response to listing SupportedDatabaseFlags.", "id": "ListSupportedDatabaseFlagsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "supportedDatabaseFlags": {"description": "The list of SupportedDatabaseFlags.", "items": {"$ref": "SupportedDatabaseFlag"}, "type": "array"}}, "type": "object"}, "ListUsersResponse": {"description": "Message for response to listing Users", "id": "ListUsersResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}, "users": {"description": "The list of User", "items": {"$ref": "User"}, "type": "array"}}, "type": "object"}, "MachineConfig": {"description": "MachineConfig describes the configuration of a machine.", "id": "MachineConfig", "properties": {"cpuCount": {"description": "The number of CPU's in the VM instance.", "format": "int32", "type": "integer"}, "machineType": {"description": "Machine type of the VM instance. E.g. \"n2-highmem-4\", \"n2-highmem-8\", \"c4a-highmem-4-lssd\". cpu_count must match the number of vCPUs in the machine type.", "type": "string"}}, "type": "object"}, "MaintenanceSchedule": {"description": "MaintenanceSchedule stores the maintenance schedule generated from the MaintenanceUpdatePolicy, once a maintenance rollout is triggered, if MaintenanceWindow is set, and if there is no conflicting DenyPeriod. The schedule is cleared once the update takes place. This field cannot be manually changed; modify the MaintenanceUpdatePolicy instead.", "id": "MaintenanceSchedule", "properties": {"startTime": {"description": "Output only. The scheduled start time for the maintenance.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MaintenanceUpdatePolicy": {"description": "MaintenanceUpdatePolicy defines the policy for system updates.", "id": "MaintenanceUpdatePolicy", "properties": {"denyMaintenancePeriods": {"description": "Periods to deny maintenance. Currently limited to 1.", "items": {"$ref": "DenyMaintenancePeriod"}, "type": "array"}, "maintenanceWindows": {"description": "Preferred windows to perform maintenance. Currently limited to 1.", "items": {"$ref": "MaintenanceWindow"}, "type": "array"}}, "type": "object"}, "MaintenanceWindow": {"description": "MaintenanceWindow specifies a preferred day and time for maintenance.", "id": "MaintenanceWindow", "properties": {"day": {"description": "Preferred day of the week for maintenance, e.g. MONDAY, TUESDAY, etc.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "startTime": {"$ref": "GoogleTypeTimeOfDay", "description": "Preferred time to start the maintenance operation on the specified day. Maintenance will start within 1 hour of this time."}}, "type": "object"}, "MigrationSource": {"description": "Subset of the source instance configuration that is available when reading the cluster resource.", "id": "MigrationSource", "properties": {"hostPort": {"description": "Output only. The host and port of the on-premises instance in host:port format", "readOnly": true, "type": "string"}, "referenceId": {"description": "Output only. Place holder for the external source identifier(e.g DMS job name) that created the cluster.", "readOnly": true, "type": "string"}, "sourceType": {"description": "Output only. Type of migration source.", "enum": ["MIGRATION_SOURCE_TYPE_UNSPECIFIED", "DMS"], "enumDescriptions": ["Migration source is unknown.", "DMS source means the cluster was created via DMS migration job."], "readOnly": true, "type": "string"}}, "type": "object"}, "NetworkConfig": {"description": "Metadata related to network configuration.", "id": "NetworkConfig", "properties": {"allocatedIpRange": {"description": "Optional. Name of the allocated IP range for the private IP AlloyDB cluster, for example: \"google-managed-services-default\". If set, the instance IPs for this cluster will be created in the allocated range. The range name must comply with RFC 1035. Specifically, the name must be 1-63 characters long and match the regular expression `[a-z]([-a-z0-9]*[a-z0-9])?`. Field name is intended to be consistent with Cloud SQL.", "type": "string"}, "network": {"description": "Optional. The resource link for the VPC network in which cluster resources are created and from which they are accessible via Private IP. The network must belong to the same project as the cluster. It is specified in the form: `projects/{project_number}/global/networks/{network_id}`. This is required to create a cluster.", "type": "string"}}, "type": "object"}, "Node": {"description": "Details of a single node in the instance. Nodes in an AlloyDB instance are ephemeral, they can change during update, failover, autohealing and resize operations.", "id": "Node", "properties": {"id": {"description": "Output only. The identifier of the VM e.g. \"test-read-0601-407e52be-ms3l\".", "readOnly": true, "type": "string"}, "ip": {"description": "Output only. The private IP address of the VM e.g. \"**********\".", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Determined by state of the compute VM and postgres-service health. Compute VM state can have values listed in https://cloud.google.com/compute/docs/instances/instance-life-cycle and postgres-service health can have values: HEALTHY and UNHEALTHY.", "readOnly": true, "type": "string"}, "zoneId": {"description": "Output only. The Compute Engine zone of the VM e.g. \"us-central1-b\".", "readOnly": true, "type": "string"}}, "type": "object"}, "ObservabilityInstanceConfig": {"description": "Observability Instance specific configuration.", "id": "ObservabilityInstanceConfig", "properties": {"assistiveExperiencesEnabled": {"description": "Whether assistive experiences are enabled for this AlloyDB instance.", "type": "boolean"}, "enabled": {"description": "Observability feature status for an instance. This flag is turned \"off\" by default.", "type": "boolean"}, "maxQueryStringLength": {"description": "Query string length. The default value is 10k.", "format": "int32", "type": "integer"}, "preserveComments": {"description": "Preserve comments in query string for an instance. This flag is turned \"off\" by default.", "type": "boolean"}, "queryPlansPerMinute": {"description": "Number of query execution plans captured by Insights per minute for all queries combined. The default value is 200. Any integer between 0 to 200 is considered valid.", "format": "int32", "type": "integer"}, "recordApplicationTags": {"description": "Record application tags for an instance. This flag is turned \"off\" by default.", "type": "boolean"}, "trackActiveQueries": {"description": "Track actively running queries on the instance. If not set, this flag is \"off\" by default.", "type": "boolean"}, "trackClientAddress": {"description": "Track client address for an instance. If not set, default value is \"off\".", "type": "boolean"}, "trackWaitEventTypes": {"description": "Output only. Track wait event types during query execution for an instance. This flag is turned \"on\" by default but tracking is enabled only after observability enabled flag is also turned on. This is read-only flag and only modifiable by internal API.", "readOnly": true, "type": "boolean"}, "trackWaitEvents": {"description": "Track wait events during query execution for an instance. This flag is turned \"on\" by default but tracking is enabled only after observability enabled flag is also turned on.", "type": "boolean"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "upgradeClusterStatus": {"$ref": "UpgradeClusterStatus", "description": "Output only. UpgradeClusterStatus related metadata.", "readOnly": true}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PrimaryConfig": {"description": "Configuration for the primary cluster. It has the list of clusters that are replicating from this cluster. This should be set if and only if the cluster is of type PRIMARY.", "id": "PrimaryConfig", "properties": {"secondaryClusterNames": {"description": "Output only. Names of the clusters that are replicating from this cluster.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "PromoteClusterRequest": {"description": "Message for promoting a Cluster", "id": "PromoteClusterRequest", "properties": {"etag": {"description": "Optional. The current etag of the Cluster. If an etag is provided and does not match the current etag of the Cluster, deletion will be blocked and an ABORTED error will be returned.", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}}, "type": "object"}, "PscAutoConnectionConfig": {"description": "Configuration for setting up PSC service automation. Consumer projects in the configs will be allowlisted automatically for the instance.", "id": "PscAutoConnectionConfig", "properties": {"consumerNetwork": {"description": "The consumer network for the PSC service automation, example: \"projects/vpc-host-project/global/networks/default\". The consumer network might be hosted a different project than the consumer project.", "type": "string"}, "consumerNetworkStatus": {"description": "Output only. The status of the service connection policy. Possible values: \"STATE_UNSPECIFIED\" - Default state, when Connection Map is created initially. \"VALID\" - Set when policy and map configuration is valid, and their matching can lead to allowing creation of PSC Connections subject to other constraints like connections limit. \"CONNECTION_POLICY_MISSING\" - No Service Connection Policy found for this network and Service Class \"POLICY_LIMIT_REACHED\" - Service Connection Policy limit reached for this network and Service Class \"CONSUMER_INSTANCE_PROJECT_NOT_ALLOWLISTED\" - The consumer instance project is not in AllowedGoogleProducersResourceHierarchyLevels of the matching ServiceConnectionPolicy.", "readOnly": true, "type": "string"}, "consumerProject": {"description": "The consumer project to which the PSC service automation endpoint will be created.", "type": "string"}, "ipAddress": {"description": "Output only. The IP address of the PSC service automation endpoint.", "readOnly": true, "type": "string"}, "status": {"description": "Output only. The status of the PSC service automation connection. Possible values: \"STATE_UNSPECIFIED\" - An invalid state as the default case. \"ACTIVE\" - The connection has been created successfully. \"FAILED\" - The connection is not functional since some resources on the connection fail to be created. \"CREATING\" - The connection is being created. \"DELETING\" - The connection is being deleted. \"CREATE_REPAIRING\" - The connection is being repaired to complete creation. \"DELETE_REPAIRING\" - The connection is being repaired to complete deletion.", "readOnly": true, "type": "string"}}, "type": "object"}, "PscConfig": {"description": "PscConfig contains PSC related configuration at a cluster level.", "id": "PscConfig", "properties": {"pscEnabled": {"description": "Optional. Create an instance that allows connections from Private Service Connect endpoints to the instance.", "type": "boolean"}, "serviceOwnedProjectNumber": {"description": "Output only. The project number that needs to be allowlisted on the network attachment to enable outbound connectivity.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "PscInstanceConfig": {"description": "PscInstanceConfig contains PSC related configuration at an instance level.", "id": "PscInstanceConfig", "properties": {"allowedConsumerProjects": {"description": "Optional. List of consumer projects that are allowed to create PSC endpoints to service-attachments to this instance.", "items": {"type": "string"}, "type": "array"}, "pscAutoConnections": {"description": "Optional. Configurations for setting up PSC service automation.", "items": {"$ref": "PscAutoConnectionConfig"}, "type": "array"}, "pscDnsName": {"description": "Output only. The DNS name of the instance for PSC connectivity. Name convention: ...alloydb-psc.goog", "readOnly": true, "type": "string"}, "pscInterfaceConfigs": {"description": "Optional. Configurations for setting up PSC interfaces attached to the instance which are used for outbound connectivity. Only primary instances can have PSC interface attached. Currently we only support 0 or 1 PSC interface.", "items": {"$ref": "PscInterfaceConfig"}, "type": "array"}, "serviceAttachmentLink": {"description": "Output only. The service attachment created when Private Service Connect (PSC) is enabled for the instance. The name of the resource will be in the format of `projects//regions//serviceAttachments/`", "readOnly": true, "type": "string"}}, "type": "object"}, "PscInterfaceConfig": {"description": "Configuration for setting up a PSC interface to enable outbound connectivity.", "id": "PscInterfaceConfig", "properties": {"networkAttachmentResource": {"description": "The network attachment resource created in the consumer network to which the PSC interface will be linked. This is of the format: \"projects/${CONSUMER_PROJECT}/regions/${REGION}/networkAttachments/${NETWORK_ATTACHMENT_NAME}\". The network attachment must be in the same region as the instance.", "type": "string"}}, "type": "object"}, "QuantityBasedExpiry": {"description": "A backup's position in a quantity-based retention queue, of backups with the same source cluster and type, with length, retention, specified by the backup's retention policy. Once the position is greater than the retention, the backup is eligible to be garbage collected. Example: 5 backups from the same source cluster and type with a quantity-based retention of 3 and denoted by backup_id (position, retention). Safe: backup_5 (1, 3), backup_4, (2, 3), backup_3 (3, 3). Awaiting garbage collection: backup_2 (4, 3), backup_1 (5, 3)", "id": "QuantityBasedExpiry", "properties": {"retentionCount": {"description": "Output only. The backup's position among its backups with the same source cluster and type, by descending chronological order create time(i.e. newest first).", "format": "int32", "readOnly": true, "type": "integer"}, "totalRetentionCount": {"description": "Output only. The length of the quantity-based queue, specified by the backup's retention policy.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "QuantityBasedRetention": {"description": "A quantity based policy specifies that a certain number of the most recent successful backups should be retained.", "id": "QuantityBasedRetention", "properties": {"count": {"description": "The number of backups to retain.", "format": "int32", "type": "integer"}}, "type": "object"}, "QueryInsightsInstanceConfig": {"description": "QueryInsights Instance specific configuration.", "id": "QueryInsightsInstanceConfig", "properties": {"queryPlansPerMinute": {"description": "Number of query execution plans captured by Insights per minute for all queries combined. The default value is 5. Any integer between 0 and 20 is considered valid.", "format": "uint32", "type": "integer"}, "queryStringLength": {"description": "Query string length. The default value is 1024. Any integer between 256 and 4500 is considered valid.", "format": "uint32", "type": "integer"}, "recordApplicationTags": {"description": "Record application tags for an instance. This flag is turned \"on\" by default.", "type": "boolean"}, "recordClientAddress": {"description": "Record client address for an instance. Client address is PII information. This flag is turned \"on\" by default.", "type": "boolean"}}, "type": "object"}, "ReadPoolConfig": {"description": "Configuration for a read pool instance.", "id": "ReadPoolConfig", "properties": {"nodeCount": {"description": "Read capacity, i.e. number of nodes in a read pool instance.", "format": "int32", "type": "integer"}}, "type": "object"}, "ReadPoolInstancesUpgradeStageStatus": {"description": "Read pool instances upgrade specific status.", "id": "ReadPoolInstancesUpgradeStageStatus", "properties": {"upgradeStats": {"$ref": "Stats", "description": "Read pool instances upgrade statistics."}}, "type": "object"}, "RestartInstanceRequest": {"id": "RestartInstanceRequest", "properties": {"nodeIds": {"description": "Optional. Full name of the nodes as obtained from INSTANCE_VIEW_FULL to restart upon. Applicable only to read instances.", "items": {"type": "string"}, "type": "array"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}}, "type": "object"}, "RestoreClusterRequest": {"description": "Message for restoring a Cluster from a backup or another cluster at a given point in time. NEXT_ID: 11", "id": "RestoreClusterRequest", "properties": {"backupSource": {"$ref": "BackupSource", "description": "Backup source."}, "cluster": {"$ref": "Cluster", "description": "Required. The resource being created"}, "clusterId": {"description": "Required. ID of the requesting object.", "type": "string"}, "continuousBackupSource": {"$ref": "ContinuousBackupSource", "description": "ContinuousBackup source. Continuous backup needs to be enabled in the source cluster for this operation to succeed."}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}}, "type": "object"}, "RestoreFromCloudSQLRequest": {"description": "Message for registering Restoring from CloudSQL resource.", "id": "RestoreFromCloudSQLRequest", "properties": {"cloudsqlBackupRunSource": {"$ref": "CloudSQLBackupRunSource", "description": "Cluster created from CloudSQL backup run."}, "cluster": {"$ref": "Cluster", "description": "Required. The resource being created"}, "clusterId": {"description": "Required. ID of the requesting object.", "type": "string"}}, "type": "object"}, "SecondaryConfig": {"description": "Configuration information for the secondary cluster. This should be set if and only if the cluster is of type SECONDARY.", "id": "SecondaryConfig", "properties": {"primaryClusterName": {"description": "The name of the primary cluster name with the format: * projects/{project}/locations/{region}/clusters/{cluster_id}", "type": "string"}}, "type": "object"}, "SqlExportOptions": {"description": "Options for exporting data in SQL format.", "id": "SqlExportOptions", "properties": {"cleanTargetObjects": {"description": "Optional. If true, output commands to DROP all the dumped database objects prior to outputting the commands for creating them.", "type": "boolean"}, "ifExistTargetObjects": {"description": "Optional. If true, use DROP ... IF EXISTS commands to check for the object's existence before dropping it in clean_target_objects mode.", "type": "boolean"}, "schemaOnly": {"description": "Optional. If true, only export the schema.", "type": "boolean"}, "tables": {"description": "Optional. Tables to export from.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SqlImportOptions": {"description": "Options for importing data in SQL format.", "id": "SqlImportOptions", "properties": {}, "type": "object"}, "SslConfig": {"description": "SSL configuration.", "id": "SslConfig", "properties": {"caSource": {"description": "Optional. Certificate Authority (CA) source. Only CA_SOURCE_MANAGED is supported currently, and is the default value.", "enum": ["CA_SOURCE_UNSPECIFIED", "CA_SOURCE_MANAGED"], "enumDescriptions": ["Certificate Authority (CA) source not specified. Defaults to CA_SOURCE_MANAGED.", "Certificate Authority (CA) managed by the AlloyDB Cluster."], "type": "string"}, "sslMode": {"description": "Optional. SSL mode. Specifies client-server SSL/TLS connection behavior.", "enum": ["SSL_MODE_UNSPECIFIED", "SSL_MODE_ALLOW", "SSL_MODE_REQUIRE", "SSL_MODE_VERIFY_CA", "ALLOW_UNENCRYPTED_AND_ENCRYPTED", "ENCRYPTED_ONLY"], "enumDeprecated": [false, true, true, true, false, false], "enumDescriptions": ["SSL mode is not specified. Defaults to ENCRYPTED_ONLY.", "SSL connections are optional. CA verification not enforced.", "SSL connections are required. CA verification not enforced. Clients may use locally self-signed certificates (default psql client behavior).", "SSL connections are required. CA verification enforced. Clients must have certificates signed by a Cluster CA, for example, using GenerateClientCertificate.", "SSL connections are optional. CA verification not enforced.", "SSL connections are required. CA verification not enforced."], "type": "string"}}, "type": "object"}, "StageInfo": {"description": "Stage information for different stages in the upgrade process.", "id": "StageInfo", "properties": {"logsUrl": {"description": "logs_url is the URL for the logs associated with a stage if that stage has logs. Right now, only three stages have logs: ALLOYDB_PRECHECK, PG_UPGRADE_CHECK, PRIMARY_INSTANCE_UPGRADE.", "type": "string"}, "stage": {"description": "The stage.", "enum": ["STAGE_UNSPECIFIED", "ALLOYDB_PRECHECK", "PG_UPGRADE_CHECK", "PREPARE_FOR_UPGRADE", "PRIMARY_INSTANCE_UPGRADE", "READ_POOL_INSTANCES_UPGRADE", "ROLLBACK", "CLEANUP"], "enumDescriptions": ["Unspecified stage.", "Pre-upgrade custom checks, not covered by pg_upgrade.", "Pre-upgrade pg_upgrade checks.", "Clone the original cluster.", "Upgrade the primary instance(downtime).", "This stage is read pool upgrade.", "Rollback in case of critical failures.", "Cleanup."], "type": "string"}, "status": {"description": "Status of the stage.", "enum": ["STATUS_UNSPECIFIED", "NOT_STARTED", "IN_PROGRESS", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "CANCEL_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Unspecified status.", "Not started.", "In progress.", "Operation succeeded.", "Operation failed.", "Operation partially succeeded.", "Cancel is in progress.", "Cancellation complete."], "type": "string"}}, "type": "object"}, "StageStatus": {"description": "Status of an upgrade stage.", "id": "StageStatus", "properties": {"readPoolInstancesUpgrade": {"$ref": "ReadPoolInstancesUpgradeStageStatus", "description": "Read pool instances upgrade metadata."}, "stage": {"description": "Upgrade stage.", "enum": ["STAGE_UNSPECIFIED", "ALLOYDB_PRECHECK", "PG_UPGRADE_CHECK", "PREPARE_FOR_UPGRADE", "PRIMARY_INSTANCE_UPGRADE", "READ_POOL_INSTANCES_UPGRADE", "ROLLBACK", "CLEANUP"], "enumDescriptions": ["Unspecified stage.", "Pre-upgrade custom checks, not covered by pg_upgrade.", "Pre-upgrade pg_upgrade checks.", "Clone the original cluster.", "Upgrade the primary instance(downtime).", "This stage is read pool upgrade.", "Rollback in case of critical failures.", "Cleanup."], "type": "string"}, "state": {"description": "State of this stage.", "enum": ["STATUS_UNSPECIFIED", "NOT_STARTED", "IN_PROGRESS", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "CANCEL_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Unspecified status.", "Not started.", "In progress.", "Operation succeeded.", "Operation failed.", "Operation partially succeeded.", "Cancel is in progress.", "Cancellation complete."], "type": "string"}}, "type": "object"}, "Stats": {"description": "Upgrade stats for read pool instances.", "id": "Stats", "properties": {"failed": {"description": "Number of read pool instances which failed to upgrade.", "format": "int32", "type": "integer"}, "notStarted": {"description": "Number of read pool instances for which upgrade has not started.", "format": "int32", "type": "integer"}, "ongoing": {"description": "Number of read pool instances undergoing upgrade.", "format": "int32", "type": "integer"}, "success": {"description": "Number of read pool instances successfully upgraded.", "format": "int32", "type": "integer"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration": {"description": "Configuration for availability of database instance", "id": "StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration", "properties": {"automaticFailoverRoutingConfigured": {"description": "Checks for existence of (multi-cluster) routing configuration that allows automatic failover to a different zone/region in case of an outage. Applicable to Bigtable resources.", "type": "boolean"}, "availabilityType": {"description": "Availability type. Potential values: * `ZONAL`: The instance serves data from only one zone. Outages in that zone affect data accessibility. * `REGIONAL`: The instance can serve data from more than one zone in a region (it is highly available).", "enum": ["AVAILABILITY_TYPE_UNSPECIFIED", "ZONAL", "REGIONAL", "MULTI_REGIONAL", "AVAILABILITY_TYPE_OTHER"], "enumDescriptions": ["", "Zonal available instance.", "Regional available instance.", "Multi regional instance", "For rest of the other category"], "type": "string"}, "crossRegionReplicaConfigured": {"description": "Checks for resources that are configured to have redundancy, and ongoing replication across regions", "type": "boolean"}, "externalReplicaConfigured": {"type": "boolean"}, "promotableReplicaConfigured": {"type": "boolean"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainBackupConfiguration": {"description": "Configuration for automatic backups", "id": "StorageDatabasecenterPartnerapiV1mainBackupConfiguration", "properties": {"automatedBackupEnabled": {"description": "Whether customer visible automated backups are enabled on the instance.", "type": "boolean"}, "backupRetentionSettings": {"$ref": "StorageDatabasecenterPartnerapiV1mainRetentionSettings", "description": "Backup retention settings."}, "pointInTimeRecoveryEnabled": {"description": "Whether point-in-time recovery is enabled. This is optional field, if the database service does not have this feature or metadata is not available in control plane, this can be omitted.", "type": "boolean"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainBackupRun": {"description": "A backup run.", "id": "StorageDatabasecenterPartnerapiV1mainBackupRun", "properties": {"endTime": {"description": "The time the backup operation completed. REQUIRED", "format": "google-datetime", "type": "string"}, "error": {"$ref": "StorageDatabasecenterPartnerapiV1mainOperationError", "description": "Information about why the backup operation failed. This is only present if the run has the FAILED status. OPTIONAL"}, "startTime": {"description": "The time the backup operation started. REQUIRED", "format": "google-datetime", "type": "string"}, "status": {"description": "The status of this run. REQUIRED", "enum": ["STATUS_UNSPECIFIED", "SUCCESSFUL", "FAILED"], "enumDescriptions": ["", "The backup was successful.", "The backup was unsuccessful."], "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainCompliance": {"description": "Contains compliance information about a security standard indicating unmet recommendations.", "id": "StorageDatabasecenterPartnerapiV1mainCompliance", "properties": {"standard": {"description": "Industry-wide compliance standards or benchmarks, such as CIS, PCI, and OWASP.", "type": "string"}, "version": {"description": "Version of the standard or benchmark, for example, 1.1", "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainCustomMetadataData": {"description": "Any custom metadata associated with the resource. e.g. A spanner instance can have multiple databases with its own unique metadata. Information for these individual databases can be captured in custom metadata data", "id": "StorageDatabasecenterPartnerapiV1mainCustomMetadataData", "properties": {"internalResourceMetadata": {"description": "Metadata for individual internal resources in an instance. e.g. spanner instance can have multiple databases with unique configuration.", "items": {"$ref": "StorageDatabasecenterPartnerapiV1mainInternalResourceMetadata"}, "type": "array"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainDatabaseResourceFeed": {"description": "DatabaseResourceFeed is the top level proto to be used to ingest different database resource level events into Condor platform. Next ID: 8", "id": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceFeed", "properties": {"feedTimestamp": {"description": "Required. Timestamp when feed is generated.", "format": "google-datetime", "type": "string"}, "feedType": {"description": "Required. Type feed to be ingested into condor", "enum": ["FEEDTYPE_UNSPECIFIED", "RESOURCE_METADATA", "OBSERVABILITY_DATA", "SECURITY_FINDING_DATA", "RECOMMENDATION_SIGNAL_DATA"], "enumDescriptions": ["", "Database resource metadata feed from control plane", "Database resource monitoring data", "Database resource security health signal data", "Database resource recommendation signal data"], "type": "string"}, "observabilityMetricData": {"$ref": "StorageDatabasecenterPartnerapiV1mainObservabilityMetricData"}, "recommendationSignalData": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceRecommendationSignalData"}, "resourceHealthSignalData": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceHealthSignalData"}, "resourceId": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceId", "deprecated": true, "description": "Primary key associated with the Resource. resource_id is available in individual feed level as well."}, "resourceMetadata": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainDatabaseResourceHealthSignalData": {"description": "Common model for database resource health signal data.", "id": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceHealthSignalData", "properties": {"additionalMetadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Any other additional metadata", "type": "object"}, "compliance": {"description": "Industry standards associated with this signal; if this signal is an issue, that could be a violation of the associated industry standard(s). For example, AUTO_BACKUP_DISABLED signal is associated with CIS GCP 1.1, CIS GCP 1.2, CIS GCP 1.3, NIST 800-53 and ISO-27001 compliance standards. If a database resource does not have automated backup enable, it will violate these following industry standards.", "items": {"$ref": "StorageDatabasecenterPartnerapiV1mainCompliance"}, "type": "array"}, "description": {"description": "Description associated with signal", "type": "string"}, "eventTime": {"description": "Required. The last time at which the event described by this signal took place", "format": "google-datetime", "type": "string"}, "externalUri": {"description": "The external-uri of the signal, using which more information about this signal can be obtained. In GCP, this will take user to SCC page to get more details about signals.", "type": "string"}, "location": {"description": "This is used to identify the location of the resource. Example: \"us-central1\"", "type": "string"}, "name": {"description": "Required. The name of the signal, ex: PUBLIC_SQL_INSTANCE, SQL_LOG_ERROR_VERBOSITY etc.", "type": "string"}, "provider": {"description": "Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged", "enum": ["PROVIDER_UNSPECIFIED", "GCP", "AWS", "AZURE", "ONPREM", "SELFMANAGED", "PROVIDER_OTHER"], "enumDescriptions": ["", "Google cloud platform provider", "Amazon web service", "Azure web service", "On-prem database resources.", "Self-managed database provider. These are resources on a cloud platform, e.g., database resource installed in a GCE VM, but not a managed database service.", "For the rest of the other categories. Other refers to the rest of other database service providers, this could be smaller cloud provider. This needs to be provided when the provider is known, but it is not present in the existing set of enum values."], "type": "string"}, "resourceContainer": {"description": "Closest parent container of this resource. In GCP, 'container' refers to a Cloud Resource Manager project. It must be resource name of a Cloud Resource Manager project with the format of \"provider//\", such as \"projects/123\". For GCP provided resources, number should be project number.", "type": "string"}, "resourceName": {"description": "Required. Database resource name associated with the signal. Resource name to follow CAIS resource_name format as noted here go/condor-common-datamodel", "type": "string"}, "signalClass": {"description": "Required. The class of the signal, such as if it's a THREAT or VULNERABILITY.", "enum": ["CLASS_UNSPECIFIED", "THREAT", "VULNERABILITY", "MISCONFIGURATION", "OBSERVATION", "ERROR"], "enumDescriptions": ["Unspecified signal class.", "Describes unwanted or malicious activity.", "Describes a potential weakness in software that increases risk to Confidentiality & Integrity & Availability.", "Describes a potential weakness in cloud resource/asset configuration that increases risk.", "Describes a security observation that is for informational purposes.", "Describes an error that prevents some SCC functionality."], "type": "string"}, "signalId": {"description": "Required. Unique identifier for the signal. This is an unique id which would be mainatined by partner to identify a signal.", "type": "string"}, "signalSeverity": {"description": "The severity of the signal, such as if it's a HIGH or LOW severity.", "enum": ["SIGNAL_SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["This value is used for findings when a source doesn't write a severity value.", "A critical vulnerability is easily discoverable by an external actor, exploitable.", "A high risk vulnerability can be easily discovered and exploited in combination with other vulnerabilities.", "A medium risk vulnerability could be used by an actor to gain access to resources or privileges that enable them to eventually gain access and the ability to execute arbitrary code or exfiltrate data.", "A low risk vulnerability hampers a security organization's ability to detect vulnerabilities or active threats in their deployment."], "type": "string"}, "signalType": {"description": "Required. Type of signal, for example, `AVAILABLE_IN_MULTIPLE_ZONES`, `LOGGING_MOST_ERRORS`, etc.", "enum": ["SIGNAL_TYPE_UNSPECIFIED", "SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER", "SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS", "SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES", "SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS", "SIGNAL_TYPE_NO_PROMOTABLE_REPLICA", "SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY", "SIGNAL_TYPE_SHORT_BACKUP_RETENTION", "SIGNAL_TYPE_LAST_BACKUP_FAILED", "SIGNAL_TYPE_LAST_BACKUP_OLD", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0", "SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0", "SIGNAL_TYPE_VIOLATES_NIST_800_53", "SIGNAL_TYPE_VIOLATES_NIST_800_53_R5", "SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0", "SIGNAL_TYPE_VIOLATES_ISO_27001", "SIGNAL_TYPE_VIOLATES_ISO_27001_V2022", "SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1", "SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0", "SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4", "SIGNAL_TYPE_VIOLATES_HIPAA", "SIGNAL_TYPE_VIOLATES_SOC2_V2017", "SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING", "SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED", "SIGNAL_TYPE_VERBOSE_ERROR_LOGGING", "SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED", "SIGNAL_TYPE_LOGGING_MOST_ERRORS", "SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS", "SIGNAL_TYPE_MINIMAL_ERROR_LOGGING", "SIGNAL_TYPE_QUERY_STATISTICS_LOGGED", "SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME", "SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS", "SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS", "SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS", "SIGNAL_TYPE_LOGGING_QUERY_STATISTICS", "SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES", "SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED", "SIGNAL_TYPE_USER_OPTIONS_CONFIGURED", "SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS", "SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS", "SIGNAL_TYPE_NO_ROOT_PASSWORD", "SIGNAL_TYPE_WEAK_ROOT_PASSWORD", "SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED", "SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED", "SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING", "SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS", "SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS", "SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED", "SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED", "SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO", "SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS", "SIGNAL_TYPE_DATABASE_NAMES_EXPOSED", "SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED", "SIGNAL_TYPE_PUBLIC_IP_ENABLED", "SIGNAL_TYPE_IDLE", "SIGNAL_TYPE_OVERPROVISIONED", "SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES", "SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES", "SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION", "SIGNAL_TYPE_UNDERPROVISIONED", "SIGNAL_TYPE_OUT_OF_DISK", "SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY", "SIGNAL_TYPE_DATABASE_AUDITING_DISABLED", "SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS", "SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP", "SIGNAL_TYPE_QUOTA_LIMIT", "SIGNAL_TYPE_NO_PASSWORD_POLICY", "SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT", "SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT", "SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT", "SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES", "SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES", "SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS", "SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET", "SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET", "SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM", "SIGNAL_TYPE_NO_USER_PASSWORD_POLICY", "SIGNAL_TYPE_HOT_NODE", "SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY", "SIGNAL_TYPE_RESOURCE_SUSPENDED", "SIGNAL_TYPE_EXPENSIVE_COMMANDS", "SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED", "SIGNAL_TYPE_NO_DELETION_PROTECTION", "SIGNAL_TYPE_INEFFICIENT_QUERY", "SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD", "SIGNAL_TYPE_MEMORY_LIMIT", "SIGNAL_TYPE_MAX_SERVER_MEMORY", "SIGNAL_TYPE_LARGE_ROWS", "SIGNAL_TYPE_HIGH_WRITE_PRESSURE", "SIGNAL_TYPE_HIGH_READ_PRESSURE", "SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED", "SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED"], "enumDeprecated": [false, false, false, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unspecified.", "Represents if a resource is protected by automatic failover. Checks for resources that are configured to have redundancy within a region that enables automatic failover.", "Represents if a group is replicating across regions. Checks for resources that are configured to have redundancy, and ongoing replication, across regions.", "Represents if the resource is available in multiple zones or not.", "Represents if a resource is available in multiple regions.", "Represents if a resource has a promotable replica.", "Represents if a resource has an automated backup policy.", "Represents if a resources has a short backup retention period.", "Represents if the last backup of a resource failed.", "Represents if the last backup of a resource is older than some threshold value.", "Represents if a resource violates CIS GCP Foundation 2.0.", "Represents if a resource violates CIS GCP Foundation 1.3.", "Represents if a resource violates CIS GCP Foundation 1.2.", "Represents if a resource violates CIS GCP Foundation 1.1.", "Represents if a resource violates CIS GCP Foundation 1.0.", "Represents if a resource violates CIS Controls 8.0.", "Represents if a resource violates NIST 800-53.", "Represents if a resource violates NIST 800-53 R5.", "Represents if a resource violates NIST Cybersecurity Framework 1.0.", "Represents if a resource violates ISO-27001.", "Represents if a resource violates ISO 27001 2022.", "Represents if a resource violates PCI-DSS v3.2.1.", "Represents if a resource violates PCI-DSS v4.0.", "Represents if a resource violates Cloud Controls Matrix v4.0.", "Represents if a resource violates HIPAA.", "Represents if a resource violates SOC2 v2017.", "Represents if log_checkpoints database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_duration database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_error_verbosity database flag for a Cloud SQL for PostgreSQL instance is not set to default or stricter (default or terse).", "Represents if the log_lock_waits database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_min_error_statement database flag for a Cloud SQL for PostgreSQL instance is not set appropriately.", "Represents if the log_min_error_statement database flag for a Cloud SQL for PostgreSQL instance does not have an appropriate severity level.", "Represents if the log_min_messages database flag for a Cloud SQL for PostgreSQL instance is not set to warning or another recommended value.", "Represents if the databaseFlags property of instance metadata for the log_executor_status field is set to on.", "Represents if the log_hostname database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_parser_stats database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_planner_stats database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_statement database flag for a Cloud SQL for PostgreSQL instance is not set to DDL (all data definition statements).", "Represents if the log_statement_stats database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_temp_files database flag for a Cloud SQL for PostgreSQL instance is not set to \"0\". (NOTE: 0 = ON)", "Represents if the user connections database flag for a Cloud SQL for SQL Server instance is configured.", "Represents if the user options database flag for Cloud SQL SQL Server instance is configured or not.", "Represents if a resource is exposed to public access.", "Represents if a resources requires all incoming connections to use SSL or not.", "Represents if a Cloud SQL database has a password configured for the root account or not.", "Represents if a Cloud SQL database has a weak password configured for the root account.", "Represents if a SQL database instance is not encrypted with customer-managed encryption keys (CMEK).", "Represents if The contained database authentication database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if the cross_db_ownership_chaining database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if he external scripts enabled database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if the local_infile database flag for a Cloud SQL for MySQL instance is not set to off.", "Represents if the log_connections database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_disconnections database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_min_duration_statement database flag for a Cloud SQL for PostgreSQL instance is not set to -1.", "Represents if the remote access database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if the skip_show_database database flag for a Cloud SQL for MySQL instance is not set to on.", "Represents if the 3625 (trace flag) database flag for a Cloud SQL for SQL Server instance is not set to on.", "Represents if public IP is enabled.", "Represents Idle instance helps to reduce costs.", "Represents instances that are unnecessarily large for given workload.", "Represents high number of concurrently opened tables.", "Represents high table count close to SLA limit.", "Represents high number of unvacuumed transactions", "Represents need for more CPU and/or memory", "Represents out of disk.", "Represents server certificate is near expiry.", "Represents database auditing is disabled.", "Represents not restricted to authorized networks.", "Represents violate org policy restrict public ip.", "Cluster nearing quota limit", "No password policy set on resources", "Performance impact of connections settings", "Performance impact of temporary tables settings", "Performance impact of transaction logs settings", "Performance impact of high joins without indexes", "Detects events where a Cloud SQL superuser (postgres for PostgreSQL servers or root for MySQL users) writes to non-system tables.", "Detects events where a database user or role has been granted all privileges to a database, or to all tables, procedures, or functions in a schema.", "Detects if database instance data exported to a Cloud Storage bucket outside of the organization.", "Detects if database instance data exported to a Cloud Storage bucket that is owned by the organization and is publicly accessible.", "Detects if a database instance is using a weak password hash algorithm.", "Detects if a database instance has no user password policy set.", "Detects if a database instance/cluster has a hot node.", "Detects if a database instance has no point in time recovery enabled.", "Detects if a database instance/cluster is suspended.", "Detects that expensive commands are being run on a database instance impacting overall performance.", "Indicates that the instance does not have a maintenance policy configured.", "Deletion Protection Disabled for the resource", "Indicates that the instance has inefficient queries detected.", "Indicates that the instance has read intensive workload.", "Indicates that the instance is nearing memory limit.", "Indicates that the instance's max server memory is configured higher than the recommended value.", "Indicates that the database has large rows beyond the recommended limit.", "Heavy write pressure on the database rows.", "Heavy read pressure on the database rows.", "Encryption org policy not satisfied.", "Location org policy not satisfied."], "type": "string"}, "state": {"enum": ["STATE_UNSPECIFIED", "ACTIVE", "RESOLVED", "MUTED"], "enumDescriptions": ["Unspecified state.", "The signal requires attention and has not been addressed yet.", "The signal has been fixed, triaged as a non-issue or otherwise addressed and is no longer active.", "The signal has been muted."], "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainDatabaseResourceId": {"description": "DatabaseResourceId will serve as primary key for any resource ingestion event.", "id": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceId", "properties": {"provider": {"description": "Required. Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged", "enum": ["PROVIDER_UNSPECIFIED", "GCP", "AWS", "AZURE", "ONPREM", "SELFMANAGED", "PROVIDER_OTHER"], "enumDescriptions": ["", "Google cloud platform provider", "Amazon web service", "Azure web service", "On-prem database resources.", "Self-managed database provider. These are resources on a cloud platform, e.g., database resource installed in a GCE VM, but not a managed database service.", "For the rest of the other categories. Other refers to the rest of other database service providers, this could be smaller cloud provider. This needs to be provided when the provider is known, but it is not present in the existing set of enum values."], "type": "string"}, "providerDescription": {"description": "Optional. Needs to be used only when the provider is PROVIDER_OTHER.", "type": "string"}, "resourceType": {"description": "Required. The type of resource this ID is identifying. Ex go/keep-sorted start alloydb.googleapis.com/Cluster, alloydb.googleapis.com/Instance, bigtableadmin.googleapis.com/Cluster, bigtableadmin.googleapis.com/Instance compute.googleapis.com/Instance firestore.googleapis.com/Database, redis.googleapis.com/Instance, redis.googleapis.com/Cluster, oracledatabase.googleapis.com/CloudExadataInfrastructure oracledatabase.googleapis.com/CloudVmCluster oracledatabase.googleapis.com/AutonomousDatabase spanner.googleapis.com/Instance, spanner.googleapis.com/Database, sqladmin.googleapis.com/Instance, go/keep-sorted end REQUIRED Please refer go/condor-common-datamodel", "type": "string"}, "uniqueId": {"description": "Required. A service-local token that distinguishes this resource from other resources within the same service.", "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata": {"description": "Common model for database resource instance metadata. Next ID: 25", "id": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata", "properties": {"availabilityConfiguration": {"$ref": "StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration", "description": "Availability configuration for this instance"}, "backupConfiguration": {"$ref": "StorageDatabasecenterPartnerapiV1mainBackupConfiguration", "description": "Backup configuration for this instance"}, "backupRun": {"$ref": "StorageDatabasecenterPartnerapiV1mainBackupRun", "description": "Latest backup run information for this instance"}, "creationTime": {"description": "The creation time of the resource, i.e. the time when resource is created and recorded in partner service.", "format": "google-datetime", "type": "string"}, "currentState": {"description": "Current state of the instance.", "enum": ["STATE_UNSPECIFIED", "HEALTHY", "UNHEALTHY", "SUSPENDED", "DELETED", "STATE_OTHER"], "enumDescriptions": ["", "The instance is running.", "Instance being created, updated, deleted or under maintenance", "When instance is suspended", "Instance is deleted.", "For rest of the other category"], "type": "string"}, "customMetadata": {"$ref": "StorageDatabasecenterPartnerapiV1mainCustomMetadataData", "description": "Any custom metadata associated with the resource"}, "edition": {"description": "Optional. Edition represents whether the instance is ENTERPRISE or ENTERPRISE_PLUS. This information is core to Cloud SQL only and is used to identify the edition of the instance.", "enum": ["EDITION_UNSPECIFIED", "EDITION_ENTERPRISE", "EDITION_ENTERPRISE_PLUS"], "enumDescriptions": ["Default, to make it consistent with instance edition enum.", "Represents the enterprise edition.", "Represents the enterprise plus edition."], "type": "string"}, "entitlements": {"description": "Entitlements associated with the resource", "items": {"$ref": "StorageDatabasecenterPartnerapiV1mainEntitlement"}, "type": "array"}, "expectedState": {"description": "The state that the instance is expected to be in. For example, an instance state can transition to UNHEALTHY due to wrong patch update, while the expected state will remain at the HEALTHY.", "enum": ["STATE_UNSPECIFIED", "HEALTHY", "UNHEALTHY", "SUSPENDED", "DELETED", "STATE_OTHER"], "enumDescriptions": ["", "The instance is running.", "Instance being created, updated, deleted or under maintenance", "When instance is suspended", "Instance is deleted.", "For rest of the other category"], "type": "string"}, "gcbdrConfiguration": {"$ref": "StorageDatabasecenterPartnerapiV1mainGCBDRConfiguration", "description": "GCBDR configuration for the resource."}, "id": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceId", "description": "Required. Unique identifier for a Database resource"}, "instanceType": {"description": "The type of the instance. Specified at creation time.", "enum": ["INSTANCE_TYPE_UNSPECIFIED", "SUB_RESOURCE_TYPE_UNSPECIFIED", "PRIMARY", "SECONDARY", "READ_REPLICA", "OTHER", "SUB_RESOURCE_TYPE_PRIMARY", "SUB_RESOURCE_TYPE_SECONDARY", "SUB_RESOURCE_TYPE_READ_REPLICA", "SUB_RESOURCE_TYPE_EXTERNAL_PRIMARY", "SUB_RESOURCE_TYPE_OTHER"], "enumDeprecated": [true, false, true, true, true, true, false, false, false, false, false], "enumDescriptions": ["Unspecified.", "For rest of the other categories.", "A regular primary database instance.", "A cluster or an instance acting as a secondary.", "An instance acting as a read-replica.", "For rest of the other categories.", "A regular primary database instance.", "A cluster or an instance acting as a secondary.", "An instance acting as a read-replica.", "An instance acting as an external primary.", "For rest of the other categories."], "type": "string"}, "location": {"description": "The resource location. REQUIRED", "type": "string"}, "machineConfiguration": {"$ref": "StorageDatabasecenterPartnerapiV1mainMachineConfiguration", "description": "Machine configuration for this resource."}, "primaryResourceId": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceId", "description": "Identifier for this resource's immediate parent/primary resource if the current resource is a replica or derived form of another Database resource. Else it would be NULL. REQUIRED if the immediate parent exists when first time resource is getting ingested, otherwise optional."}, "primaryResourceLocation": {"description": "Primary resource location. REQUIRED if the immediate parent exists when first time resource is getting ingested, otherwise optional.", "type": "string"}, "product": {"$ref": "StorageDatabasecenterProtoCommonProduct", "description": "The product this resource represents."}, "resourceContainer": {"description": "Closest parent Cloud Resource Manager container of this resource. It must be resource name of a Cloud Resource Manager project with the format of \"/\", such as \"projects/123\". For GCP provided resources, number should be project number.", "type": "string"}, "resourceName": {"description": "Required. Different from DatabaseResourceId.unique_id, a resource name can be reused over time. That is, after a resource named \"ABC\" is deleted, the name \"ABC\" can be used to to create a new resource within the same source. Resource name to follow CAIS resource_name format as noted here go/condor-common-datamodel", "type": "string"}, "suspensionReason": {"description": "Optional. Suspension reason for the resource.", "enum": ["SUSPENSION_REASON_UNSPECIFIED", "WIPEOUT_HIDE_EVENT", "WIPEOUT_PURGE_EVENT", "BILLING_DISABLED", "ABUSER_DETECTED", "ENCRYPTION_KEY_INACCESSIBLE", "REPLICATED_CLUSTER_ENCRYPTION_KEY_INACCESSIBLE"], "enumDescriptions": ["Suspension reason is unspecified.", "Wipeout hide event.", "Wipeout purge event.", "Billing disabled for project", "Abuse detected for resource", "Encryption key inaccessible.", "Replicated cluster encryption key inaccessible."], "type": "string"}, "tagsSet": {"$ref": "StorageDatabasecenterPartnerapiV1mainTags", "description": "Optional. Tags associated with this resources."}, "updationTime": {"description": "The time at which the resource was updated and recorded at partner service.", "format": "google-datetime", "type": "string"}, "userLabelSet": {"$ref": "StorageDatabasecenterPartnerapiV1mainUserLabels", "description": "User-provided labels associated with the resource"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainDatabaseResourceRecommendationSignalData": {"description": "Common model for database resource recommendation signal data.", "id": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceRecommendationSignalData", "properties": {"additionalMetadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. Any other additional metadata specific to recommendation", "type": "object"}, "lastRefreshTime": {"description": "Required. last time <PERSON>w as refreshed", "format": "google-datetime", "type": "string"}, "recommendationState": {"description": "Required. Recommendation state", "enum": ["UNSPECIFIED", "ACTIVE", "CLAIMED", "SUCCEEDED", "FAILED", "DISMISSED"], "enumDescriptions": ["", "Recommendation is active and can be applied. ACTIVE recommendations can be marked as CLAIMED, SUCCEEDED, or FAILED.", "Recommendation is in claimed state. Recommendations content is immutable and cannot be updated by Google. CLAIMED recommendations can be marked as CLAIMED, SUCCEEDED, or FAILED.", "Recommendation is in succeeded state. Recommendations content is immutable and cannot be updated by Google. SUCCEEDED recommendations can be marked as SUCCEEDED, or FAILED.", "Recommendation is in failed state. Recommendations content is immutable and cannot be updated by Google. FAILED recommendations can be marked as SUCCEEDED, or FAILED.", "Recommendation is in dismissed state. Recommendation content can be updated by Google. DISMISSED recommendations can be marked as ACTIVE."], "type": "string"}, "recommender": {"description": "Required. Name of recommendation. Examples: organizations/1234/locations/us-central1/recommenders/google.cloudsql.instance.PerformanceRecommender/recommendations/9876", "type": "string"}, "recommenderId": {"description": "Required. ID of recommender. Examples: \"google.cloudsql.instance.PerformanceRecommender\"", "type": "string"}, "recommenderSubtype": {"description": "Required. Contains an identifier for a subtype of recommendations produced for the same recommender. Subtype is a function of content and impact, meaning a new subtype might be added when significant changes to `content` or `primary_impact.category` are introduced. See the Recommenders section to see a list of subtypes for a given Recommender. Examples: For recommender = \"google.cloudsql.instance.PerformanceRecommender\", recommender_subtype can be \"MYSQL_HIGH_NUMBER_OF_OPEN_TABLES_BEST_PRACTICE\"/\"POSTGRES_HIGH_TRANSACTION_ID_UTILIZATION_BEST_PRACTICE\"", "type": "string"}, "resourceName": {"description": "Required. Database resource name associated with the signal. Resource name to follow CAIS resource_name format as noted here go/condor-common-datamodel", "type": "string"}, "signalType": {"description": "Required. Type of signal, for example, `SIGNAL_TYPE_IDLE`, `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.", "enum": ["SIGNAL_TYPE_UNSPECIFIED", "SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER", "SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS", "SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES", "SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS", "SIGNAL_TYPE_NO_PROMOTABLE_REPLICA", "SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY", "SIGNAL_TYPE_SHORT_BACKUP_RETENTION", "SIGNAL_TYPE_LAST_BACKUP_FAILED", "SIGNAL_TYPE_LAST_BACKUP_OLD", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1", "SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0", "SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0", "SIGNAL_TYPE_VIOLATES_NIST_800_53", "SIGNAL_TYPE_VIOLATES_NIST_800_53_R5", "SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0", "SIGNAL_TYPE_VIOLATES_ISO_27001", "SIGNAL_TYPE_VIOLATES_ISO_27001_V2022", "SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1", "SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0", "SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4", "SIGNAL_TYPE_VIOLATES_HIPAA", "SIGNAL_TYPE_VIOLATES_SOC2_V2017", "SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING", "SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED", "SIGNAL_TYPE_VERBOSE_ERROR_LOGGING", "SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED", "SIGNAL_TYPE_LOGGING_MOST_ERRORS", "SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS", "SIGNAL_TYPE_MINIMAL_ERROR_LOGGING", "SIGNAL_TYPE_QUERY_STATISTICS_LOGGED", "SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME", "SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS", "SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS", "SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS", "SIGNAL_TYPE_LOGGING_QUERY_STATISTICS", "SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES", "SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED", "SIGNAL_TYPE_USER_OPTIONS_CONFIGURED", "SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS", "SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS", "SIGNAL_TYPE_NO_ROOT_PASSWORD", "SIGNAL_TYPE_WEAK_ROOT_PASSWORD", "SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED", "SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED", "SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING", "SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS", "SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS", "SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED", "SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED", "SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO", "SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS", "SIGNAL_TYPE_DATABASE_NAMES_EXPOSED", "SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED", "SIGNAL_TYPE_PUBLIC_IP_ENABLED", "SIGNAL_TYPE_IDLE", "SIGNAL_TYPE_OVERPROVISIONED", "SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES", "SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES", "SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION", "SIGNAL_TYPE_UNDERPROVISIONED", "SIGNAL_TYPE_OUT_OF_DISK", "SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY", "SIGNAL_TYPE_DATABASE_AUDITING_DISABLED", "SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS", "SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP", "SIGNAL_TYPE_QUOTA_LIMIT", "SIGNAL_TYPE_NO_PASSWORD_POLICY", "SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT", "SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT", "SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT", "SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES", "SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES", "SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS", "SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET", "SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET", "SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM", "SIGNAL_TYPE_NO_USER_PASSWORD_POLICY", "SIGNAL_TYPE_HOT_NODE", "SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY", "SIGNAL_TYPE_RESOURCE_SUSPENDED", "SIGNAL_TYPE_EXPENSIVE_COMMANDS", "SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED", "SIGNAL_TYPE_NO_DELETION_PROTECTION", "SIGNAL_TYPE_INEFFICIENT_QUERY", "SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD", "SIGNAL_TYPE_MEMORY_LIMIT", "SIGNAL_TYPE_MAX_SERVER_MEMORY", "SIGNAL_TYPE_LARGE_ROWS", "SIGNAL_TYPE_HIGH_WRITE_PRESSURE", "SIGNAL_TYPE_HIGH_READ_PRESSURE", "SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED", "SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED"], "enumDeprecated": [false, false, false, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unspecified.", "Represents if a resource is protected by automatic failover. Checks for resources that are configured to have redundancy within a region that enables automatic failover.", "Represents if a group is replicating across regions. Checks for resources that are configured to have redundancy, and ongoing replication, across regions.", "Represents if the resource is available in multiple zones or not.", "Represents if a resource is available in multiple regions.", "Represents if a resource has a promotable replica.", "Represents if a resource has an automated backup policy.", "Represents if a resources has a short backup retention period.", "Represents if the last backup of a resource failed.", "Represents if the last backup of a resource is older than some threshold value.", "Represents if a resource violates CIS GCP Foundation 2.0.", "Represents if a resource violates CIS GCP Foundation 1.3.", "Represents if a resource violates CIS GCP Foundation 1.2.", "Represents if a resource violates CIS GCP Foundation 1.1.", "Represents if a resource violates CIS GCP Foundation 1.0.", "Represents if a resource violates CIS Controls 8.0.", "Represents if a resource violates NIST 800-53.", "Represents if a resource violates NIST 800-53 R5.", "Represents if a resource violates NIST Cybersecurity Framework 1.0.", "Represents if a resource violates ISO-27001.", "Represents if a resource violates ISO 27001 2022.", "Represents if a resource violates PCI-DSS v3.2.1.", "Represents if a resource violates PCI-DSS v4.0.", "Represents if a resource violates Cloud Controls Matrix v4.0.", "Represents if a resource violates HIPAA.", "Represents if a resource violates SOC2 v2017.", "Represents if log_checkpoints database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_duration database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_error_verbosity database flag for a Cloud SQL for PostgreSQL instance is not set to default or stricter (default or terse).", "Represents if the log_lock_waits database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_min_error_statement database flag for a Cloud SQL for PostgreSQL instance is not set appropriately.", "Represents if the log_min_error_statement database flag for a Cloud SQL for PostgreSQL instance does not have an appropriate severity level.", "Represents if the log_min_messages database flag for a Cloud SQL for PostgreSQL instance is not set to warning or another recommended value.", "Represents if the databaseFlags property of instance metadata for the log_executor_status field is set to on.", "Represents if the log_hostname database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_parser_stats database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_planner_stats database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_statement database flag for a Cloud SQL for PostgreSQL instance is not set to DDL (all data definition statements).", "Represents if the log_statement_stats database flag for a Cloud SQL for PostgreSQL instance is not set to off.", "Represents if the log_temp_files database flag for a Cloud SQL for PostgreSQL instance is not set to \"0\". (NOTE: 0 = ON)", "Represents if the user connections database flag for a Cloud SQL for SQL Server instance is configured.", "Represents if the user options database flag for Cloud SQL SQL Server instance is configured or not.", "Represents if a resource is exposed to public access.", "Represents if a resources requires all incoming connections to use SSL or not.", "Represents if a Cloud SQL database has a password configured for the root account or not.", "Represents if a Cloud SQL database has a weak password configured for the root account.", "Represents if a SQL database instance is not encrypted with customer-managed encryption keys (CMEK).", "Represents if The contained database authentication database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if the cross_db_ownership_chaining database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if he external scripts enabled database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if the local_infile database flag for a Cloud SQL for MySQL instance is not set to off.", "Represents if the log_connections database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_disconnections database flag for a Cloud SQL for PostgreSQL instance is not set to on.", "Represents if the log_min_duration_statement database flag for a Cloud SQL for PostgreSQL instance is not set to -1.", "Represents if the remote access database flag for a Cloud SQL for SQL Server instance is not set to off.", "Represents if the skip_show_database database flag for a Cloud SQL for MySQL instance is not set to on.", "Represents if the 3625 (trace flag) database flag for a Cloud SQL for SQL Server instance is not set to on.", "Represents if public IP is enabled.", "Represents Idle instance helps to reduce costs.", "Represents instances that are unnecessarily large for given workload.", "Represents high number of concurrently opened tables.", "Represents high table count close to SLA limit.", "Represents high number of unvacuumed transactions", "Represents need for more CPU and/or memory", "Represents out of disk.", "Represents server certificate is near expiry.", "Represents database auditing is disabled.", "Represents not restricted to authorized networks.", "Represents violate org policy restrict public ip.", "Cluster nearing quota limit", "No password policy set on resources", "Performance impact of connections settings", "Performance impact of temporary tables settings", "Performance impact of transaction logs settings", "Performance impact of high joins without indexes", "Detects events where a Cloud SQL superuser (postgres for PostgreSQL servers or root for MySQL users) writes to non-system tables.", "Detects events where a database user or role has been granted all privileges to a database, or to all tables, procedures, or functions in a schema.", "Detects if database instance data exported to a Cloud Storage bucket outside of the organization.", "Detects if database instance data exported to a Cloud Storage bucket that is owned by the organization and is publicly accessible.", "Detects if a database instance is using a weak password hash algorithm.", "Detects if a database instance has no user password policy set.", "Detects if a database instance/cluster has a hot node.", "Detects if a database instance has no point in time recovery enabled.", "Detects if a database instance/cluster is suspended.", "Detects that expensive commands are being run on a database instance impacting overall performance.", "Indicates that the instance does not have a maintenance policy configured.", "Deletion Protection Disabled for the resource", "Indicates that the instance has inefficient queries detected.", "Indicates that the instance has read intensive workload.", "Indicates that the instance is nearing memory limit.", "Indicates that the instance's max server memory is configured higher than the recommended value.", "Indicates that the database has large rows beyond the recommended limit.", "Heavy write pressure on the database rows.", "Heavy read pressure on the database rows.", "Encryption org policy not satisfied.", "Location org policy not satisfied."], "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainEntitlement": {"description": "Proto representing the access that a user has to a specific feature/service. NextId: 3.", "id": "StorageDatabasecenterPartnerapiV1mainEntitlement", "properties": {"entitlementState": {"description": "The current state of user's accessibility to a feature/benefit.", "enum": ["ENTITLEMENT_STATE_UNSPECIFIED", "ENTITLED", "REVOKED"], "enumDescriptions": ["", "User is entitled to a feature/benefit, but whether it has been successfully provisioned is decided by provisioning state.", "User is entitled to a feature/benefit, but it was requested to be revoked. Whether the revoke has been successful is decided by provisioning state."], "type": "string"}, "type": {"description": "An enum that represents the type of this entitlement.", "enum": ["ENTITLEMENT_TYPE_UNSPECIFIED", "GEMINI", "NATIVE", "GCA_STANDARD"], "enumDeprecated": [false, true, false, false], "enumDescriptions": ["The entitlement type is unspecified.", "The root entitlement representing Gemini package ownership.This will no longer be supported in the future.", "The entitlement representing Native Tier, This will be the default Entitlement going forward with GCA Enablement.", "The entitlement representing GCA-Standard Tier."], "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainGCBDRConfiguration": {"description": "GCBDR Configuration for the resource.", "id": "StorageDatabasecenterPartnerapiV1mainGCBDRConfiguration", "properties": {"gcbdrManaged": {"description": "Whether the resource is managed by GCBDR.", "type": "boolean"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainInternalResourceMetadata": {"description": "Metadata for individual internal resources in an instance. e.g. spanner instance can have multiple databases with unique configuration settings. Similarly bigtable can have multiple clusters within same bigtable instance.", "id": "StorageDatabasecenterPartnerapiV1mainInternalResourceMetadata", "properties": {"backupConfiguration": {"$ref": "StorageDatabasecenterPartnerapiV1mainBackupConfiguration", "description": "Backup configuration for this database"}, "backupRun": {"$ref": "StorageDatabasecenterPartnerapiV1mainBackupRun", "description": "Information about the last backup attempt for this database"}, "isDeletionProtectionEnabled": {"description": "Whether deletion protection is enabled for this internal resource.", "type": "boolean"}, "product": {"$ref": "StorageDatabasecenterProtoCommonProduct"}, "resourceId": {"$ref": "StorageDatabasecenterPartnerapiV1mainDatabaseResourceId"}, "resourceName": {"description": "Required. internal resource name for spanner this will be database name e.g.\"spanner.googleapis.com/projects/123/abc/instances/inst1/databases/db1\"", "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainMachineConfiguration": {"description": "MachineConfiguration describes the configuration of a machine specific to Database Resource.", "id": "StorageDatabasecenterPartnerapiV1mainMachineConfiguration", "properties": {"cpuCount": {"deprecated": true, "description": "The number of CPUs. Deprecated. Use vcpu_count instead. TODO(b/342344482) add proto validations again after bug fix.", "format": "int32", "type": "integer"}, "memorySizeInBytes": {"description": "Memory size in bytes. TODO(b/342344482) add proto validations again after bug fix.", "format": "int64", "type": "string"}, "shardCount": {"description": "Optional. Number of shards (if applicable).", "format": "int32", "type": "integer"}, "vcpuCount": {"description": "Optional. The number of vCPUs. TODO(b/342344482) add proto validations again after bug fix.", "format": "double", "type": "number"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainObservabilityMetricData": {"id": "StorageDatabasecenterPartnerapiV1mainObservabilityMetricData", "properties": {"aggregationType": {"description": "Required. Type of aggregation performed on the metric.", "enum": ["AGGREGATION_TYPE_UNSPECIFIED", "PEAK", "P99", "P95", "CURRENT"], "enumDescriptions": ["Unspecified aggregation type.", "PEAK aggregation type.", "P99 aggregation type.", "P95 aggregation type.", "current aggregation type."], "type": "string"}, "metricType": {"description": "Required. Type of metric like CPU, Memory, etc.", "enum": ["METRIC_TYPE_UNSPECIFIED", "CPU_UTILIZATION", "MEMORY_UTILIZATION", "NETWORK_CONNECTIONS", "STORAGE_UTILIZATION", "STORAGE_USED_BYTES", "NODE_COUNT", "MEMORY_USED_BYTES", "PROCESSING_UNIT_COUNT"], "enumDescriptions": ["Unspecified metric type.", "CPU utilization for a resource. The value is a fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some cases).", "Memory utilization for a resource. The value is a fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some cases).", "Number of network connections for a resource.", "Storage utilization for a resource. The value is a fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some cases).", "Sotrage used by a resource.", "Node count for a resource. It represents the number of node units in a bigtable/spanner instance.", "Memory used by a resource (in bytes).", "Processing units used by a resource. It represents the number of processing units in a spanner instance."], "type": "string"}, "observationTime": {"description": "Required. The time the metric value was observed.", "format": "google-datetime", "type": "string"}, "resourceName": {"description": "Required. Database resource name associated with the signal. Resource name to follow CAIS resource_name format as noted here go/condor-common-datamodel", "type": "string"}, "value": {"$ref": "StorageDatabasecenterProtoCommonTypedValue", "description": "Required. Value of the metric type."}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainOperationError": {"description": "An error that occurred during a backup creation operation.", "id": "StorageDatabasecenterPartnerapiV1mainOperationError", "properties": {"code": {"description": "Identifies the specific error that occurred. REQUIRED", "type": "string"}, "errorType": {"enum": ["OPERATION_ERROR_TYPE_UNSPECIFIED", "KMS_KEY_ERROR", "DATABASE_ERROR", "STOCKOUT_ERROR", "CANCELLATION_ERROR", "SQLSERVER_ERROR", "INTERNAL_ERROR"], "enumDescriptions": ["UNSPECIFIED means product type is not known or available.", "key destroyed, expired, not found, unreachable or permission denied.", "Database is not accessible", "The zone or region does not have sufficient resources to handle the request at the moment", "User initiated cancellation", "SQL server specific error", "Any other internal error."], "type": "string"}, "message": {"description": "Additional information about the error encountered. REQUIRED", "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainRetentionSettings": {"id": "StorageDatabasecenterPartnerapiV1mainRetentionSettings", "properties": {"durationBasedRetention": {"description": "Duration based retention period i.e. 172800 seconds (2 days)", "format": "google-duration", "type": "string"}, "quantityBasedRetention": {"format": "int32", "type": "integer"}, "retentionUnit": {"deprecated": true, "description": "The unit that 'retained_backups' represents.", "enum": ["RETENTION_UNIT_UNSPECIFIED", "COUNT", "TIME", "DURATION", "RETENTION_UNIT_OTHER"], "enumDescriptions": ["Backup retention unit is unspecified, will be treated as COUNT.", "Retention will be by count, eg. \"retain the most recent 7 backups\".", "Retention will be by Time, eg. \"retain backups till a specific time\" i.e. till 2024-05-01T00:00:00Z.", "Retention will be by duration, eg. \"retain the backups for 172800 seconds (2 days)\".", "For rest of the other category"], "type": "string"}, "timeBasedRetention": {"deprecated": true, "format": "google-duration", "type": "string"}, "timestampBasedRetentionTime": {"description": "Timestamp based retention period i.e. 2024-05-01T00:00:00Z", "format": "google-datetime", "type": "string"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainTags": {"description": "Message type for storing tags. Tags provide a way to create annotations for resources, and in some cases conditionally allow or deny policies based on whether a resource has a specific tag.", "id": "StorageDatabasecenterPartnerapiV1mainTags", "properties": {"tags": {"additionalProperties": {"type": "string"}, "description": "The Tag key/value mappings.", "type": "object"}}, "type": "object"}, "StorageDatabasecenterPartnerapiV1mainUserLabels": {"description": "Message type for storing user labels. User labels are used to tag App Engine resources, allowing users to search for resources matching a set of labels and to aggregate usage data by labels.", "id": "StorageDatabasecenterPartnerapiV1mainUserLabels", "properties": {"labels": {"additionalProperties": {"type": "string"}, "type": "object"}}, "type": "object"}, "StorageDatabasecenterProtoCommonProduct": {"description": "Product specification for Condor resources.", "id": "StorageDatabasecenterProtoCommonProduct", "properties": {"engine": {"description": "The specific engine that the underlying database is running.", "enum": ["ENGINE_UNSPECIFIED", "ENGINE_MYSQL", "MYSQL", "ENGINE_POSTGRES", "POSTGRES", "ENGINE_SQL_SERVER", "SQL_SERVER", "ENGINE_NATIVE", "NATIVE", "ENGINE_CLOUD_SPANNER_WITH_POSTGRES_DIALECT", "ENGINE_CLOUD_SPANNER_WITH_GOOGLESQL_DIALECT", "ENGINE_MEMORYSTORE_FOR_REDIS", "ENGINE_MEMORYSTORE_FOR_REDIS_CLUSTER", "ENGINE_OTHER", "ENGINE_FIRESTORE_WITH_NATIVE_MODE", "ENGINE_FIRESTORE_WITH_DATASTORE_MODE", "ENGINE_EXADATA_ORACLE", "ENGINE_ADB_SERVERLESS_ORACLE"], "enumDeprecated": [false, false, true, false, true, false, true, false, true, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["UNSPECIFIED means engine type is not known or available.", "MySQL binary running as an engine in the database instance.", "MySQL binary running as engine in database instance.", "Postgres binary running as engine in database instance.", "Postgres binary running as engine in database instance.", "SQLServer binary running as engine in database instance.", "SQLServer binary running as engine in database instance.", "Native database binary running as engine in instance.", "Native database binary running as engine in instance.", "Cloud Spanner with PostgreSQL dialect.", "Cloud Spanner with Google SQL dialect.", "Memorystore with Redis dialect.", "Memorystore with Redis cluster dialect.", "Other refers to rest of other database engine. This is to be when engine is known, but it is not present in this enum.", "Firestore with native mode.", "Firestore with datastore mode.", "Oracle Exadata engine.", "Oracle Autonomous DB Serverless engine."], "type": "string"}, "type": {"description": "Type of specific database product. It could be CloudSQL, AlloyDB etc..", "enum": ["PRODUCT_TYPE_UNSPECIFIED", "PRODUCT_TYPE_CLOUD_SQL", "CLOUD_SQL", "PRODUCT_TYPE_ALLOYDB", "ALLOYDB", "PRODUCT_TYPE_SPANNER", "PRODUCT_TYPE_ON_PREM", "ON_PREM", "PRODUCT_TYPE_MEMORYSTORE", "PRODUCT_TYPE_BIGTABLE", "PRODUCT_TYPE_FIRESTORE", "PRODUCT_TYPE_COMPUTE_ENGINE", "PRODUCT_TYPE_ORACLE_ON_GCP", "PRODUCT_TYPE_OTHER"], "enumDeprecated": [false, false, true, false, true, false, false, true, false, false, false, false, false, false], "enumDescriptions": ["UNSPECIFIED means product type is not known or available.", "Cloud SQL product area in GCP", "Cloud SQL product area in GCP", "AlloyDB product area in GCP", "AlloyDB product area in GCP", "Spanner product area in GCP", "On premises database product.", "On premises database product.", "Memorystore product area in GCP", "Bigtable product area in GCP", "Firestore product area in GCP.", "Compute Engine self managed databases", "Oracle product area in GCP", "Other refers to rest of other product type. This is to be when product type is known, but it is not present in this enum."], "type": "string"}, "version": {"description": "Version of the underlying database engine. Example values: For MySQL, it could be \"8.0\", \"5.7\" etc.. For Postgres, it could be \"14\", \"15\" etc..", "type": "string"}}, "type": "object"}, "StorageDatabasecenterProtoCommonTypedValue": {"description": "TypedValue represents the value of a metric type. It can either be a double, an int64, a string or a bool.", "id": "StorageDatabasecenterProtoCommonTypedValue", "properties": {"boolValue": {"description": "For boolean value", "type": "boolean"}, "doubleValue": {"description": "For double value", "format": "double", "type": "number"}, "int64Value": {"description": "For integer value", "format": "int64", "type": "string"}, "stringValue": {"description": "For string value", "type": "string"}}, "type": "object"}, "StringRestrictions": {"description": "Restrictions on STRING type values", "id": "StringRestrictions", "properties": {"allowedValues": {"description": "The list of allowed values, if bounded. This field will be empty if there is a unbounded number of allowed values.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SupportedDatabaseFlag": {"description": "SupportedDatabaseFlag gives general information about a database flag, like type and allowed values. This is a static value that is defined on the server side, and it cannot be modified by callers. To set the Database flags on a particular Instance, a caller should modify the Instance.database_flags field.", "id": "SupportedDatabaseFlag", "properties": {"acceptsMultipleValues": {"description": "Whether the database flag accepts multiple values. If true, a comma-separated list of stringified values may be specified.", "type": "boolean"}, "flagName": {"description": "The name of the database flag, e.g. \"max_allowed_packets\". The is a possibly key for the Instance.database_flags map field.", "type": "string"}, "integerRestrictions": {"$ref": "IntegerRestrictions", "description": "Restriction on INTEGER type value."}, "name": {"description": "The name of the flag resource, following Google Cloud conventions, e.g.: * projects/{project}/locations/{location}/flags/{flag} This field currently has no semantic meaning.", "type": "string"}, "recommendedIntegerValue": {"description": "The recommended value for an INTEGER flag.", "format": "int64", "type": "string"}, "recommendedStringValue": {"description": "The recommended value for a STRING flag.", "type": "string"}, "requiresDbRestart": {"description": "Whether setting or updating this flag on an Instance requires a database restart. If a flag that requires database restart is set, the backend will automatically restart the database (making sure to satisfy any availability SLO's).", "type": "boolean"}, "scope": {"description": "The scope of the flag.", "enum": ["SCOPE_UNSPECIFIED", "DATABASE", "CONNECTION_POOL"], "enumDescriptions": ["The scope of the flag is not specified. Default is DATABASE.", "The flag is a database flag.", "The flag is a connection pool flag."], "type": "string"}, "stringRestrictions": {"$ref": "StringRestrictions", "description": "Restriction on STRING type value."}, "supportedDbVersions": {"description": "Major database engine versions for which this flag is supported.", "items": {"enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "type": "string"}, "type": "array"}, "valueType": {"enum": ["VALUE_TYPE_UNSPECIFIED", "STRING", "INTEGER", "FLOAT", "NONE"], "enumDescriptions": ["This is an unknown flag type.", "String type flag.", "Integer type flag.", "Float type flag.", "Denotes that the flag does not accept any values."], "type": "string"}}, "type": "object"}, "SwitchoverClusterRequest": {"description": "Message for switching over to a cluster", "id": "SwitchoverClusterRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}}, "type": "object"}, "TimeBasedRetention": {"description": "A time based retention policy specifies that all backups within a certain time period should be retained.", "id": "TimeBasedRetention", "properties": {"retentionPeriod": {"description": "The retention period.", "format": "google-duration", "type": "string"}}, "type": "object"}, "TrialMetadata": {"description": "Contains information and all metadata related to TRIAL clusters.", "id": "TrialMetadata", "properties": {"endTime": {"description": "End time of the trial cluster.", "format": "google-datetime", "type": "string"}, "graceEndTime": {"description": "grace end time of the cluster.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "start time of the trial cluster.", "format": "google-datetime", "type": "string"}, "upgradeTime": {"description": "Upgrade time of trial cluster to Standard cluster.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UpdatePolicy": {"description": "Policy to be used while updating the instance.", "id": "UpdatePolicy", "properties": {"mode": {"description": "Mode for updating the instance.", "enum": ["MODE_UNSPECIFIED", "DEFAULT", "FORCE_APPLY"], "enumDescriptions": ["Mode is unknown.", "Least disruptive way to apply the update.", "Performs a forced update when applicable. This will be fast but may incur a downtime."], "type": "string"}}, "type": "object"}, "UpgradeClusterRequest": {"description": "Upgrades a cluster.", "id": "UpgradeClusterRequest", "properties": {"etag": {"description": "Optional. The current etag of the Cluster. If an etag is provided and does not match the current etag of the Cluster, upgrade will be blocked and an ABORTED error will be returned.", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server ignores the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if the original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "validateOnly": {"description": "Optional. If set, performs request validation, for example, permission checks and any other type of validation, but does not actually execute the create request.", "type": "boolean"}, "version": {"description": "Required. The version the cluster is going to be upgraded to.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "type": "string"}}, "type": "object"}, "UpgradeClusterResponse": {"description": "UpgradeClusterResponse contains the response for upgrade cluster operation.", "id": "UpgradeClusterResponse", "properties": {"clusterUpgradeDetails": {"description": "Array of upgrade details for the current cluster and all the secondary clusters associated with this cluster.", "items": {"$ref": "ClusterUpgradeDetails"}, "type": "array"}, "message": {"description": "A user friendly message summarising the upgrade operation details and the next steps for the user if there is any.", "type": "string"}, "status": {"description": "Status of upgrade operation.", "enum": ["STATUS_UNSPECIFIED", "NOT_STARTED", "IN_PROGRESS", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "CANCEL_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Unspecified status.", "Not started.", "In progress.", "Operation succeeded.", "Operation failed.", "Operation partially succeeded.", "Cancel is in progress.", "Cancellation complete."], "type": "string"}}, "type": "object"}, "UpgradeClusterStatus": {"description": "Message for current status of the Major Version Upgrade operation.", "id": "UpgradeClusterStatus", "properties": {"cancellable": {"description": "Whether the operation is cancellable.", "type": "boolean"}, "sourceVersion": {"description": "Source database major version.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "type": "string"}, "stages": {"description": "Status of all upgrade stages.", "items": {"$ref": "StageStatus"}, "type": "array"}, "state": {"description": "Cluster Major Version Upgrade state.", "enum": ["STATUS_UNSPECIFIED", "NOT_STARTED", "IN_PROGRESS", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "CANCEL_IN_PROGRESS", "CANCELLED"], "enumDescriptions": ["Unspecified status.", "Not started.", "In progress.", "Operation succeeded.", "Operation failed.", "Operation partially succeeded.", "Cancel is in progress.", "Cancellation complete."], "type": "string"}, "targetVersion": {"description": "Target database major version.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16", "POSTGRES_17"], "enumDeprecated": [false, true, false, false, false, false], "enumDescriptions": ["This is an unknown database version.", "DEPRECATED - The database version is Postgres 13.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16.", "The database version is Postgres 17."], "type": "string"}}, "type": "object"}, "User": {"description": "Message describing User object.", "id": "User", "properties": {"databaseRoles": {"description": "Optional. List of database roles this user has. The database role strings are subject to the PostgreSQL naming conventions.", "items": {"type": "string"}, "type": "array"}, "keepExtraRoles": {"description": "Input only. If the user already exists and it has additional roles, keep them granted.", "type": "boolean"}, "name": {"description": "Output only. Name of the resource in the form of projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.", "readOnly": true, "type": "string"}, "password": {"description": "Input only. Password for the user.", "type": "string"}, "userType": {"description": "Optional. Type of this user.", "enum": ["USER_TYPE_UNSPECIFIED", "ALLOYDB_BUILT_IN", "ALLOYDB_IAM_USER"], "enumDescriptions": ["Unspecified user type.", "The default user type that authenticates via password-based authentication.", "Database user that can authenticate via IAM-Based authentication."], "type": "string"}}, "type": "object"}, "UserPassword": {"description": "The username/password for a database user. Used for specifying initial users at cluster creation time.", "id": "UserPassword", "properties": {"password": {"description": "The initial password for the user.", "type": "string"}, "user": {"description": "The database username.", "type": "string"}}, "type": "object"}, "WeeklySchedule": {"description": "A weekly schedule starts a backup at prescribed start times within a day, for the specified days of the week. The weekly schedule message is flexible and can be used to create many types of schedules. For example, to have a daily backup that starts at 22:00, configure the `start_times` field to have one element \"22:00\" and the `days_of_week` field to have all seven days of the week.", "id": "WeeklySchedule", "properties": {"daysOfWeek": {"description": "The days of the week to perform a backup. If this field is left empty, the default of every day of the week is used.", "items": {"enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "type": "array"}, "startTimes": {"description": "The times during the day to start a backup. The start times are assumed to be in UTC and to be an exact hour (e.g., 04:00:00). If no start times are provided, a single fixed start time is chosen arbitrarily.", "items": {"$ref": "GoogleTypeTimeOfDay"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "AlloyDB API", "version": "v1alpha", "version_module": true}