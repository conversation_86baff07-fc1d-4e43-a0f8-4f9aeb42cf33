{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/apps.alerts": {"description": "See and delete your domain's G Suite alerts, and send alert feedback"}}}}, "basePath": "", "baseUrl": "https://alertcenter.googleapis.com/", "batchPath": "batch", "canonicalName": "AlertCenter", "description": "Manages alerts on issues affecting your domain. Note: The current version of this API (v1beta1) is available to all Google Workspace customers. ", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/workspace/admin/alertcenter/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "alertcenter:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://alertcenter.mtls.googleapis.com/", "name": "alertcenter", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"alerts": {"methods": {"batchDelete": {"description": "Performs batch delete operation on alerts.", "flatPath": "v1beta1/alerts:batchDelete", "httpMethod": "POST", "id": "alertcenter.alerts.batchDelete", "parameterOrder": [], "parameters": {}, "path": "v1beta1/alerts:batchDelete", "request": {"$ref": "BatchDeleteAlertsRequest"}, "response": {"$ref": "BatchDeleteAlertsResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "batchUndelete": {"description": "Performs batch undelete operation on alerts.", "flatPath": "v1beta1/alerts:batchUndelete", "httpMethod": "POST", "id": "alertcenter.alerts.batchUndelete", "parameterOrder": [], "parameters": {}, "path": "v1beta1/alerts:batchUndelete", "request": {"$ref": "BatchUndeleteAlertsRequest"}, "response": {"$ref": "BatchUndeleteAlertsResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "delete": {"description": "Marks the specified alert for deletion. An alert that has been marked for deletion is removed from Alert Center after 30 days. Marking an alert for deletion has no effect on an alert which has already been marked for deletion. Attempting to mark a nonexistent alert for deletion results in a `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}", "httpMethod": "DELETE", "id": "alertcenter.alerts.delete", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert to delete.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert is associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "get": {"description": "Gets the specified alert. Attempting to get a nonexistent alert returns `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}", "httpMethod": "GET", "id": "alertcenter.alerts.get", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert to retrieve.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert is associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "getMetadata": {"description": "Returns the metadata of an alert. Attempting to get metadata for a non-existent alert returns `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}/metadata", "httpMethod": "GET", "id": "alertcenter.alerts.getMetadata", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert this metadata belongs to.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert metadata is associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}/metadata", "response": {"$ref": "AlertMetadata"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "list": {"description": "Lists the alerts.", "flatPath": "v1beta1/alerts", "httpMethod": "GET", "id": "alertcenter.alerts.list", "parameterOrder": [], "parameters": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alerts are associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}, "filter": {"description": "Optional. A query string for filtering alert results. For more details, see [Query filters](https://developers.google.com/workspace/admin/alertcenter/guides/query-filters) and [Supported query filter fields](https://developers.google.com/workspace/admin/alertcenter/reference/filter-fields#alerts.list).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sort order of the list results. If not specified results may be returned in arbitrary order. You can sort the results in descending order based on the creation timestamp using `order_by=\"create_time desc\"`. Currently, supported sorting are `create_time asc`, `create_time desc`, `update_time desc`", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The requested page size. Server may return fewer items than requested. If unspecified, server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return. If empty, a new iteration is started. To continue an iteration, pass in the value from the previous ListAlertsResponse's next_page_token field.", "location": "query", "type": "string"}}, "path": "v1beta1/alerts", "response": {"$ref": "ListAlertsResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "undelete": {"description": "Restores, or \"undeletes\", an alert that was marked for deletion within the past 30 days. Attempting to undelete an alert which was marked for deletion over 30 days ago (which has been removed from the Alert Center database) or a nonexistent alert returns a `NOT_FOUND` error. Attempting to undelete an alert which has not been marked for deletion has no effect.", "flatPath": "v1beta1/alerts/{alertId}:undelete", "httpMethod": "POST", "id": "alertcenter.alerts.undelete", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert to undelete.", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/alerts/{alertId}:undelete", "request": {"$ref": "UndeleteAlertRequest"}, "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}}, "resources": {"feedback": {"methods": {"create": {"description": "Creates new feedback for an alert. Attempting to create a feedback for a non-existent alert returns `NOT_FOUND` error. Attempting to create a feedback for an alert that is marked for deletion returns `FAILED_PRECONDITION' error.", "flatPath": "v1beta1/alerts/{alertId}/feedback", "httpMethod": "POST", "id": "alertcenter.alerts.feedback.create", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The identifier of the alert this feedback belongs to.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert is associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}/feedback", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "list": {"description": "Lists all the feedback for an alert. Attempting to list feedbacks for a non-existent alert returns `NOT_FOUND` error.", "flatPath": "v1beta1/alerts/{alertId}/feedback", "httpMethod": "GET", "id": "alertcenter.alerts.feedback.list", "parameterOrder": ["alertId"], "parameters": {"alertId": {"description": "Required. The alert identifier. The \"-\" wildcard could be used to represent all alerts.", "location": "path", "required": true, "type": "string"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert is associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}, "filter": {"description": "Optional. A query string for filtering alert feedback results. For more details, see [Query filters](https://developers.google.com/workspace/admin/alertcenter/guides/query-filters) and [Supported query filter fields](https://developers.google.com/workspace/admin/alertcenter/reference/filter-fields#alerts.feedback.list).", "location": "query", "type": "string"}}, "path": "v1beta1/alerts/{alertId}/feedback", "response": {"$ref": "ListAlertFeedbackResponse"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}}}}}, "v1beta1": {"methods": {"getSettings": {"description": "Returns customer-level settings.", "flatPath": "v1beta1/settings", "httpMethod": "GET", "id": "alertcenter.getSettings", "parameterOrder": [], "parameters": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert settings are associated with. The `customer_id` must/ have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}}, "path": "v1beta1/settings", "response": {"$ref": "Settings"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}, "updateSettings": {"description": "Updates the customer-level settings.", "flatPath": "v1beta1/settings", "httpMethod": "PATCH", "id": "alertcenter.updateSettings", "parameterOrder": [], "parameters": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert settings are associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "location": "query", "type": "string"}}, "path": "v1beta1/settings", "request": {"$ref": "Settings"}, "response": {"$ref": "Settings"}, "scopes": ["https://www.googleapis.com/auth/apps.alerts"]}}}}, "revision": "********", "rootUrl": "https://alertcenter.googleapis.com/", "schemas": {"AbuseDetected": {"description": "A generic alert for abusive user activity occurring with a customer.", "id": "AbuseDetected", "properties": {"additionalDetails": {"$ref": "EntityList", "description": "List of abusive users/entities to be displayed in a table in the alert."}, "product": {"description": "Product that the abuse is originating from.", "type": "string"}, "subAlertId": {"description": "Unique identifier of each sub alert that is onboarded.", "type": "string"}, "variationType": {"description": "Variation of AbuseDetected alerts. The variation_type determines the texts displayed the alert details. This differs from sub_alert_id because each sub alert can have multiple variation_types, representing different stages of the alert.", "enum": ["ABUSE_DETECTED_VARIATION_TYPE_UNSPECIFIED", "DRIVE_ABUSIVE_CONTENT", "LIMITED_DISABLE"], "enumDescriptions": ["AbuseDetected alert variation type unspecified. No alert should be unspecified.", "Variation displayed for Drive abusive content alerts.", "Variation displayed for Limited Disable alerts, when a Google service is disabled for a user, totally or partially, due to the user's abusive behavior."], "type": "string"}}, "type": "object"}, "AccessApproval": {"description": "Alert that is triggered when Google support requests to access customer data.", "id": "AccessApproval", "properties": {"justificationReason": {"description": "Justification for data access based on justification enums.", "items": {"enum": ["JUSTIFICATION_UNSPECIFIED", "CUSTOMER_INITIATED_SUPPORT", "GOOGLE_INITIATED_REVIEW", "GOOGLE_INITIATED_SERVICE", "THIRD_PARTY_DATA_REQUEST", "GOOGLE_RESPONSE_TO_PRODUCTION_ALERT"], "enumDescriptions": ["Justification unspecified", "Customer Initiated Support", "Google Initiated Review", "Google Initiated Service", "Third Party Data Request", "Google Response to Production Alert"], "type": "string"}, "type": "array"}, "officeLocation": {"description": "Office location of Google staff requesting access such as \"US\".", "type": "string"}, "products": {"description": "Products within scope of the Access Approvals request.", "items": {"type": "string"}, "type": "array"}, "requestId": {"description": "ID of the Access Approvals request. This is a helpful field when requesting support from Google.", "type": "string"}, "scope": {"description": "Scope of access, also known as a resource. This is further narrowed down by the product field.", "type": "string"}, "tickets": {"description": "Support tickets related to this Access Approvals request. Populated if there is an associated case number.", "items": {"$ref": "SupportTicket"}, "type": "array"}}, "type": "object"}, "AccountSuspensionDetails": {"description": "Details about why an account is receiving an account suspension warning.", "id": "AccountSuspensionDetails", "properties": {"abuseReason": {"description": "The reason why this account is receiving an account suspension warning.", "enum": ["ACCOUNT_SUSPENSION_ABUSE_REASON_UNSPECIFIED", "TOS_VIOLATION", "SPAM", "PHISHING", "TRAFFIC_PUMPING", "FRAUD", "NUMBER_HARVESTING", "PAYMENTS_FRAUD", "UNWANTED_CONTENT"], "enumDescriptions": ["Abuse reason is unspecified.", "This account is being suspended for a Terms of Service violation.", "This account is being suspended for spam.", "This account is being suspended for phishing.", "This account is being suspended for artificially boosting traffic to a website.", "This account is being suspended for fraud.", "This account is being suspended for number harvesting.", "This account is being suspended for payments fraud.", "This account is being suspended for unwanted content."], "type": "string"}, "productName": {"description": "The name of the product being abused. This is restricted to only the following values: \"Gmail\" \"Google Workspace\" \"Payments\" \"Voice\" \"YouTube\" \"Other\"", "type": "string"}}, "type": "object"}, "AccountSuspensionWarning": {"description": "A warning that the customer's account is about to be suspended.", "id": "AccountSuspensionWarning", "properties": {"appealWindow": {"description": "The amount of time remaining to appeal an imminent suspension. After this window has elapsed, the account will be suspended. Only populated if the account suspension is in WARNING state.", "format": "google-duration", "type": "string"}, "state": {"description": "Account suspension warning state.", "enum": ["ACCOUNT_SUSPENSION_WARNING_STATE_UNSPECIFIED", "WARNING", "SUSPENDED", "APPEAL_APPROVED", "APPEAL_SUBMITTED"], "enumDescriptions": ["State is unspecified.", "Customer is receiving a warning about imminent suspension.", "Customer is being notified that their account has been suspended.", "Customer is being notified that their suspension appeal was approved.", "Customer has submitted their appeal, which is pending review."], "type": "string"}, "suspensionDetails": {"description": "Details about why an account is being suspended.", "items": {"$ref": "AccountSuspensionDetails"}, "type": "array"}}, "type": "object"}, "AccountWarning": {"description": "Alerts for user account warning events.", "id": "Account<PERSON><PERSON>ning", "properties": {"email": {"description": "Required. The email of the user that this event belongs to.", "type": "string"}, "loginDetails": {"$ref": "LoginDetails", "description": "Optional. Details of the login action associated with the warning event. This is only available for: * Suspicious login * Suspicious login (less secure app) * Suspicious programmatic login * User suspended (suspicious activity)"}}, "type": "object"}, "ActionInfo": {"description": "Metadata related to the action.", "id": "ActionInfo", "properties": {}, "type": "object"}, "ActivityRule": {"description": "Alerts from Google Workspace Security Center rules service configured by an admin.", "id": "ActivityRule", "properties": {"actionNames": {"description": "List of action names associated with the rule threshold.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Rule create timestamp.", "format": "google-datetime", "type": "string"}, "description": {"description": "Description of the rule.", "type": "string"}, "displayName": {"description": "Alert display name.", "type": "string"}, "name": {"description": "Rule name.", "type": "string"}, "query": {"description": "Query that is used to get the data from the associated source.", "type": "string"}, "supersededAlerts": {"description": "List of alert IDs superseded by this alert. It is used to indicate that this alert is essentially extension of superseded alerts and we found the relationship after creating these alerts.", "items": {"type": "string"}, "type": "array"}, "supersedingAlert": {"description": "Alert ID superseding this alert. It is used to indicate that superseding alert is essentially extension of this alert and we found the relationship after creating both alerts.", "type": "string"}, "threshold": {"description": "Alert threshold is for example “COUNT > 5”.", "type": "string"}, "triggerSource": {"description": "The trigger sources for this rule. * GMAIL_EVENTS * DEVICE_EVENTS * USER_EVENTS", "type": "string"}, "updateTime": {"description": "The timestamp of the last update to the rule.", "format": "google-datetime", "type": "string"}, "windowSize": {"description": "Rule window size. Possible values are 1 hour or 24 hours.", "format": "google-duration", "type": "string"}}, "type": "object"}, "Alert": {"description": "An alert affecting a customer.", "id": "<PERSON><PERSON>", "properties": {"alertId": {"description": "Output only. The unique identifier for the alert.", "type": "string"}, "createTime": {"description": "Output only. The time this alert was created.", "format": "google-datetime", "type": "string"}, "customerId": {"description": "Output only. The unique identifier of the Google Workspace account of the customer.", "type": "string"}, "data": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Optional. The data associated with this alert, for example google.apps.alertcenter.type.DeviceCompromised.", "type": "object"}, "deleted": {"description": "Output only. `True` if this alert is marked for deletion.", "type": "boolean"}, "endTime": {"description": "Optional. The time the event that caused this alert ceased being active. If provided, the end time must not be earlier than the start time. If not provided, it indicates an ongoing alert.", "format": "google-datetime", "type": "string"}, "etag": {"description": "Optional. `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of an alert from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform alert updates in order to avoid race conditions: An `etag` is returned in the response which contains alerts, and systems are expected to put that etag in the request to update alert to ensure that their change will be applied to the same version of the alert. If no `etag` is provided in the call to update alert, then the existing alert is overwritten blindly.", "type": "string"}, "metadata": {"$ref": "AlertMetadata", "description": "Output only. The metadata associated with this alert."}, "securityInvestigationToolLink": {"description": "Output only. An optional [Security Investigation Tool](https://support.google.com/a/answer/7575955) query for this alert.", "type": "string"}, "source": {"description": "Required. A unique identifier for the system that reported the alert. This is output only after alert is created. Supported sources are any of the following: * Google Operations * Mobile device management * Gmail phishing * Data Loss Prevention * Domain wide takeout * State sponsored attack * Google identity * Apps outage", "type": "string"}, "startTime": {"description": "Required. The time the event that caused this alert was started or detected.", "format": "google-datetime", "type": "string"}, "type": {"description": "Required. The type of the alert. This is output only after alert is created. For a list of available alert types see [Google Workspace Alert types](https://developers.google.com/workspace/admin/alertcenter/reference/alert-types).", "type": "string"}, "updateTime": {"description": "Output only. The time this alert was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "AlertFeedback": {"description": "A customer feedback about an alert.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"alertId": {"description": "Output only. The alert identifier.", "type": "string"}, "createTime": {"description": "Output only. The time this feedback was created.", "format": "google-datetime", "type": "string"}, "customerId": {"description": "Output only. The unique identifier of the Google Workspace account of the customer.", "type": "string"}, "email": {"description": "Output only. The email of the user that provided the feedback.", "type": "string"}, "feedbackId": {"description": "Output only. The unique identifier for the feedback.", "type": "string"}, "type": {"description": "Required. The type of the feedback.", "enum": ["ALERT_FEEDBACK_TYPE_UNSPECIFIED", "NOT_USEFUL", "SOMEWHAT_USEFUL", "VERY_USEFUL"], "enumDescriptions": ["The feedback type is not specified.", "The alert report is not useful.", "The alert report is somewhat useful.", "The alert report is very useful."], "type": "string"}}, "type": "object"}, "AlertMetadata": {"description": "An alert metadata.", "id": "AlertMetadata", "properties": {"alertId": {"description": "Output only. The alert identifier.", "type": "string"}, "assignee": {"description": "The email address of the user assigned to the alert.", "type": "string"}, "customerId": {"description": "Output only. The unique identifier of the Google Workspace account of the customer.", "type": "string"}, "etag": {"description": "Optional. `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of an alert metadata from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform metadata updates in order to avoid race conditions: An `etag` is returned in the response which contains alert metadata, and systems are expected to put that etag in the request to update alert metadata to ensure that their change will be applied to the same version of the alert metadata. If no `etag` is provided in the call to update alert metadata, then the existing alert metadata is overwritten blindly.", "type": "string"}, "severity": {"description": "The severity value of the alert. Alert Center will set this field at alert creation time, default's to an empty string when it could not be determined. The supported values for update actions on this field are the following: * HIGH * MEDIUM * LOW", "type": "string"}, "status": {"description": "The current status of the alert. The supported values are the following: * NOT_STARTED * IN_PROGRESS * CLOSED", "type": "string"}, "updateTime": {"description": "Output only. The time this metadata was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ApnsCertificateExpirationInfo": {"description": "The explanation message associated with \"APNS certificate is expiring soon\" and \"APNS certificate has expired\" alerts.", "id": "ApnsCertificateExpirationInfo", "properties": {"appleId": {"description": "The Apple ID used to create the certificate. It may be blank if admins didn't enter it.", "type": "string"}, "expirationTime": {"description": "The expiration date of the APNS certificate.", "format": "google-datetime", "type": "string"}, "uid": {"description": "The UID of the certificate.", "type": "string"}}, "type": "object"}, "AppMakerSqlSetupNotification": {"description": "Alerts from App Maker to notify admins to set up default SQL instance.", "id": "AppMakerSqlSetupNotification", "properties": {"requestInfo": {"description": "List of applications with requests for default SQL set up.", "items": {"$ref": "RequestInfo"}, "type": "array"}}, "type": "object"}, "AppSettingsChanged": {"description": "Alerts from AppSettingsChanged bucket Rules configured by Admin which contain the following rules: - Calendar settings changed - Drive settings changed - Email settings changed - Mobile settings changed", "id": "AppSettingsChanged", "properties": {"alertDetails": {"description": "Any other associated alert details, for example, AlertConfiguration.", "format": "byte", "type": "string"}, "name": {"description": "Rule name", "type": "string"}}, "type": "object"}, "AppsOutage": {"description": "An outage incident reported for a Google Workspace service.", "id": "AppsOutage", "properties": {"dashboardUri": {"description": "Link to the outage event in Google Workspace Status Dashboard", "type": "string"}, "incidentTrackingId": {"description": "Incident tracking ID.", "type": "string"}, "mergeInfo": {"$ref": "MergeInfo", "description": "Indicates new alert details under which the outage is communicated. Only populated when Status is MERGED."}, "nextUpdateTime": {"description": "Timestamp by which the next update is expected to arrive.", "format": "google-datetime", "type": "string"}, "products": {"description": "List of products impacted by the outage.", "items": {"type": "string"}, "type": "array"}, "resolutionTime": {"description": "Timestamp when the outage is expected to be resolved, or has confirmed resolution. Provided only when known.", "format": "google-datetime", "type": "string"}, "status": {"description": "Current outage status.", "enum": ["STATUS_UNSPECIFIED", "NEW", "ONGOING", "RESOLVED", "FALSE_POSITIVE", "PARTIALLY_RESOLVED", "MERGED", "DOWNGRADED"], "enumDescriptions": ["Status is unspecified.", "The incident has just been reported.", "The incident is ongoing.", "The incident has been resolved.", "Further assessment indicated no customer impact.", "The incident has been partially resolved.", "The incident was merged into a parent.", "The incident has lower impact than initially anticipated."], "type": "string"}}, "type": "object"}, "Attachment": {"description": "Attachment with application-specific information about an alert.", "id": "Attachment", "properties": {"csv": {"$ref": "Csv", "description": "A CSV file attachment."}}, "type": "object"}, "BadWhitelist": {"description": "<PERSON><PERSON> for setting the domain or IP that malicious email comes from as whitelisted domain or IP in Gmail advanced settings.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"domainId": {"$ref": "DomainId", "description": "The domain ID."}, "maliciousEntity": {"$ref": "MaliciousEntity", "description": "The entity whose actions triggered a Gmail phishing alert."}, "messages": {"description": "The list of messages contained by this alert.", "items": {"$ref": "GmailMessageInfo"}, "type": "array"}, "sourceIp": {"description": "The source IP address of the malicious email, for example, `127.0.0.1`.", "type": "string"}}, "type": "object"}, "BatchDeleteAlertsRequest": {"description": "A request to perform batch delete on alerts.", "id": "BatchDeleteAlertsRequest", "properties": {"alertId": {"description": "Required. The list of alert IDs to delete.", "items": {"type": "string"}, "type": "array"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alerts are associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "type": "string"}}, "type": "object"}, "BatchDeleteAlertsResponse": {"description": "Response to batch delete operation on alerts.", "id": "BatchDeleteAlertsResponse", "properties": {"failedAlertStatus": {"additionalProperties": {"$ref": "Status"}, "description": "The status details for each failed `alert_id`.", "type": "object"}, "successAlertIds": {"description": "The successful list of alert IDs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BatchUndeleteAlertsRequest": {"description": "A request to perform batch undelete on alerts.", "id": "BatchUndeleteAlertsRequest", "properties": {"alertId": {"description": "Required. The list of alert IDs to undelete.", "items": {"type": "string"}, "type": "array"}, "customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alerts are associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "type": "string"}}, "type": "object"}, "BatchUndeleteAlertsResponse": {"description": "Response to batch undelete operation on alerts.", "id": "BatchUndeleteAlertsResponse", "properties": {"failedAlertStatus": {"additionalProperties": {"$ref": "Status"}, "description": "The status details for each failed `alert_id`.", "type": "object"}, "successAlertIds": {"description": "The successful list of alert IDs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CloudPubsubTopic": {"description": "A reference to a Cloud Pubsub topic. To register for notifications, the owner of the topic must grant `<EMAIL>` the `projects.topics.publish` permission.", "id": "CloudPubsubTopic", "properties": {"payloadFormat": {"description": "Optional. The format of the payload that would be sent. If not specified the format will be JSON.", "enum": ["PAYLOAD_FORMAT_UNSPECIFIED", "JSON"], "enumDescriptions": ["Payload format is not specified (will use JSON as default).", "Use JSON."], "type": "string"}, "topicName": {"description": "The `name` field of a Cloud Pubsub [Topic] (https://cloud.google.com/pubsub/docs/reference/rest/v1/projects.topics#Topic).", "type": "string"}}, "type": "object"}, "Csv": {"description": "A representation of a CSV file attachment, as a list of column headers and a list of data rows.", "id": "Csv", "properties": {"dataRows": {"description": "The list of data rows in a CSV file, as string arrays rather than as a single comma-separated string.", "items": {"$ref": "CsvRow"}, "type": "array"}, "headers": {"description": "The list of headers for data columns in a CSV file.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CsvRow": {"description": "A representation of a single data row in a CSV file.", "id": "CsvRow", "properties": {"entries": {"description": "The data entries in a CSV file row, as a string array rather than a single comma-separated string.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DeviceCompromised": {"description": "A mobile device compromised alert. Derived from audit logs.", "id": "DeviceCompromised", "properties": {"email": {"description": "The email of the user this alert was created for.", "type": "string"}, "events": {"description": "Required. The list of security events.", "items": {"$ref": "DeviceCompromisedSecurityDetail"}, "type": "array"}}, "type": "object"}, "DeviceCompromisedSecurityDetail": {"description": "Detailed information of a single MDM device compromised event.", "id": "DeviceCompromisedSecurityDetail", "properties": {"deviceCompromisedState": {"description": "The device compromised state. Possible values are \"`Compromised`\" or \"`Not Compromised`\".", "type": "string"}, "deviceId": {"description": "Required. The device ID.", "type": "string"}, "deviceModel": {"description": "The model of the device.", "type": "string"}, "deviceType": {"description": "The type of the device.", "type": "string"}, "iosVendorId": {"description": "Required for iOS, empty for others.", "type": "string"}, "resourceId": {"description": "The device resource ID.", "type": "string"}, "serialNumber": {"description": "The serial number of the device.", "type": "string"}}, "type": "object"}, "DeviceManagementRule": {"description": "Alerts from Device Management Rules configured by Admin.", "id": "DeviceManagementRule", "properties": {"deviceId": {"description": "Required. The device ID.", "type": "string"}, "deviceModel": {"description": "The model of the device.", "type": "string"}, "deviceType": {"description": "The type of the device.", "type": "string"}, "email": {"description": "The email of the user this alert was created for.", "type": "string"}, "id": {"description": "ID of the rule that triggered the alert", "type": "string"}, "iosVendorId": {"description": "Required for iOS, empty for others.", "type": "string"}, "ownerId": {"description": "Obfuscated ID of the owner of the device", "type": "string"}, "resourceId": {"description": "The device resource ID.", "type": "string"}, "ruleAction": {"description": "Action taken as result of the rule", "type": "string"}, "serialNumber": {"description": "The serial number of the device.", "type": "string"}}, "type": "object"}, "DlpRuleViolation": {"description": "Alerts that get triggered on violations of Data Loss Prevention (DLP) rules.", "id": "DlpRuleViolation", "properties": {"ruleViolationInfo": {"$ref": "RuleViolationInfo", "description": "Details about the violated DLP rule. <PERSON><PERSON> can use the predefined detectors provided by Google Cloud DLP https://cloud.google.com/dlp/ when setting up a DLP rule. Matched Cloud DLP detectors in this violation if any will be captured in the MatchInfo.predefined_detector."}}, "type": "object"}, "DomainId": {"description": "Domain ID of Gmail phishing alerts.", "id": "DomainId", "properties": {"customerPrimaryDomain": {"description": "The primary domain for the customer.", "type": "string"}}, "type": "object"}, "DomainWideTakeoutInitiated": {"description": "A takeout operation for the entire domain was initiated by an admin. Derived from audit logs.", "id": "DomainWideTakeoutInitiated", "properties": {"email": {"description": "The email of the admin who initiated the takeout.", "type": "string"}, "takeoutRequestId": {"description": "The takeout request ID.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Entity": {"description": "Individual entity affected by, or related to, an alert.", "id": "Entity", "properties": {"link": {"description": "Link to a Security Investigation Tool search based on this entity, if available.", "type": "string"}, "name": {"description": "Human-readable name of this entity, such as an email address, file ID, or device name.", "type": "string"}, "values": {"description": "Extra values beyond name. The order of values should align with headers in EntityList.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "EntityList": {"description": "EntityList stores entities in a format that can be translated to a table in the Alert Center UI.", "id": "EntityList", "properties": {"entities": {"description": "List of entities affected by the alert.", "items": {"$ref": "Entity"}, "type": "array"}, "headers": {"description": "Headers of the values in entities. If no value is defined in Entity, this field should be empty.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Name of the key detail used to display this entity list.", "type": "string"}}, "type": "object"}, "GmailMessageInfo": {"description": "Details of a message in phishing spike alert.", "id": "GmailMessageInfo", "properties": {"attachmentsSha256Hash": {"description": "The `SHA256` hash of email's attachment and all MIME parts.", "items": {"type": "string"}, "type": "array"}, "date": {"description": "The date of the event related to this email.", "format": "google-datetime", "type": "string"}, "md5HashMessageBody": {"description": "The hash of the message body text.", "type": "string"}, "md5HashSubject": {"description": "The MD5 Hash of email's subject (only available for reported emails).", "type": "string"}, "messageBodySnippet": {"description": "The snippet of the message body text (only available for reported emails).", "type": "string"}, "messageId": {"description": "The message ID.", "type": "string"}, "recipient": {"description": "The recipient of this email.", "type": "string"}, "sentTime": {"description": "The sent time of the email.", "format": "google-datetime", "type": "string"}, "subjectText": {"description": "The email subject text (only available for reported emails).", "type": "string"}}, "type": "object"}, "GoogleOperations": {"description": "An incident reported by Google Operations for a Google Workspace application.", "id": "GoogleOperations", "properties": {"affectedUserEmails": {"description": "The list of emails which correspond to the users directly affected by the incident.", "items": {"type": "string"}, "type": "array"}, "attachmentData": {"$ref": "Attachment", "description": "Optional. Application-specific data for an incident, provided when the Google Workspace application which reported the incident cannot be completely restored to a valid state."}, "description": {"description": "A detailed, freeform incident description.", "type": "string"}, "domain": {"description": "Customer domain for email template personalization.", "type": "string"}, "header": {"description": "A header to display above the incident message. Typically used to attach a localized notice on the timeline for followup comms translations.", "type": "string"}, "title": {"description": "A one-line incident description.", "type": "string"}}, "type": "object"}, "ListAlertFeedbackResponse": {"description": "Response message for an alert feedback listing request.", "id": "ListAlertFeedbackResponse", "properties": {"feedback": {"description": "The list of alert feedback. Feedback entries for each alert are ordered by creation time descending.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "ListAlertsResponse": {"description": "Response message for an alert listing request.", "id": "ListAlertsResponse", "properties": {"alerts": {"description": "The list of alerts.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "The token for the next page. If not empty, indicates that there may be more alerts that match the listing request; this value can be used in a subsequent ListAlertsRequest to get alerts continuing from last result of the current list call.", "type": "string"}}, "type": "object"}, "LoginDetails": {"description": "The details of the login action.", "id": "LoginDetails", "properties": {"ipAddress": {"description": "Optional. The human-readable IP address (for example, `***********`) that is associated with the warning event.", "type": "string"}, "loginTime": {"description": "Optional. The successful login time that is associated with the warning event. This isn't present for blocked login attempts.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "MailPhishing": {"description": "Proto for all phishing alerts with common payload. Supported types are any of the following: * User reported phishing * User reported spam spike * Suspicious message reported * Phishing reclassification * Malware reclassification * Gmail potential employee spoofing", "id": "MailPhishing", "properties": {"domainId": {"$ref": "DomainId", "description": "The domain ID."}, "isInternal": {"description": "If `true`, the email originated from within the organization.", "type": "boolean"}, "maliciousEntity": {"$ref": "MaliciousEntity", "description": "The entity whose actions triggered a Gmail phishing alert."}, "messages": {"description": "The list of messages contained by this alert.", "items": {"$ref": "GmailMessageInfo"}, "type": "array"}, "systemActionType": {"description": "System actions on the messages.", "enum": ["SYSTEM_ACTION_TYPE_UNSPECIFIED", "NO_OPERATION", "REMOVED_FROM_INBOX"], "enumDescriptions": ["System action is unspecified.", "No operation.", "Messages were removed from the inbox."], "type": "string"}}, "type": "object"}, "MaliciousEntity": {"description": "<PERSON><PERSON><PERSON> whose actions triggered a Gmail phishing alert.", "id": "MaliciousEntity", "properties": {"displayName": {"description": "The header from display name.", "type": "string"}, "entity": {"$ref": "User", "description": "The actor who triggered a gmail phishing alert."}, "fromHeader": {"description": "The sender email address.", "type": "string"}}, "type": "object"}, "MandatoryServiceAnnouncement": {"description": "Alert Created by the MSA team for communications necessary for continued use of Google Workspace Products.", "id": "MandatoryServiceAnnouncement", "properties": {"description": {"description": "Detailed, freeform text describing the announcement", "type": "string"}, "title": {"description": "One line summary of the announcement", "type": "string"}}, "type": "object"}, "MatchInfo": {"description": "Proto that contains match information from the condition part of the rule.", "id": "MatchInfo", "properties": {"predefinedDetector": {"$ref": "PredefinedDetectorInfo", "description": "For matched detector predefined by Google."}, "userDefinedDetector": {"$ref": "UserDefinedDetectorInfo", "description": "For matched detector defined by administrators."}}, "type": "object"}, "MergeInfo": {"description": "New alert tracking numbers.", "id": "MergeInfo", "properties": {"newAlertId": {"description": "Optional. New alert ID. Reference the [google.apps.alertcenter.Alert] with this ID for the current state.", "type": "string"}, "newIncidentTrackingId": {"description": "The new tracking ID from the parent incident.", "type": "string"}}, "type": "object"}, "Notification": {"description": "Settings for callback notifications. For more details see [Google Workspace Alert Notification](https://developers.google.com/workspace/admin/alertcenter/guides/notifications).", "id": "Notification", "properties": {"cloudPubsubTopic": {"$ref": "CloudPubsubTopic", "description": "A Google Cloud Pub/sub topic destination."}}, "type": "object"}, "PhishingSpike": {"description": "Alert for a spike in user reported phishing. *Warning*: This type has been deprecated. Use [MailPhishing](https://developers.google.com/workspace/admin/alertcenter/reference/rest/v1beta1/MailPhishing) instead.", "id": "PhishingSpike", "properties": {"domainId": {"$ref": "DomainId", "description": "The domain ID."}, "isInternal": {"description": "If `true`, the email originated from within the organization.", "type": "boolean"}, "maliciousEntity": {"$ref": "MaliciousEntity", "description": "The entity whose actions triggered a Gmail phishing alert."}, "messages": {"description": "The list of messages contained by this alert.", "items": {"$ref": "GmailMessageInfo"}, "type": "array"}}, "type": "object"}, "PredefinedDetectorInfo": {"description": "Detector provided by Google.", "id": "PredefinedDetectorInfo", "properties": {"detectorName": {"description": "Name that uniquely identifies the detector.", "type": "string"}}, "type": "object"}, "PrimaryAdminChangedEvent": {"description": "Event occurred when primary admin changed in customer's account. The event are being received from insight forwarder", "id": "PrimaryAdminChangedEvent", "properties": {"domain": {"description": "domain in which actioned occurred", "type": "string"}, "previousAdminEmail": {"description": "Email of person who was the primary admin before the action", "type": "string"}, "updatedAdminEmail": {"description": "Email of person who is the primary admin after the action", "type": "string"}}, "type": "object"}, "ReportingRule": {"description": "Alerts from Reporting Rules configured by Admin.", "id": "ReportingRule", "properties": {"alertDetails": {"description": "Any other associated alert details, for example, AlertConfiguration.", "format": "byte", "type": "string"}, "name": {"description": "Rule name", "type": "string"}, "query": {"description": "Alert Rule query Sample Query query { condition { filter { expected_application_id: ************ expected_event_name: \"indexable_content_change\" filter_op: IN } } conjunction_operator: OR }", "format": "byte", "type": "string"}}, "type": "object"}, "RequestInfo": {"description": "Requests for one application that needs default SQL setup.", "id": "RequestInfo", "properties": {"appDeveloperEmail": {"description": "List of app developers who triggered notifications for above application.", "items": {"type": "string"}, "type": "array"}, "appKey": {"description": "Required. The application that requires the SQL setup.", "type": "string"}, "numberOfRequests": {"description": "Required. Number of requests sent for this application to set up default SQL instance.", "format": "int64", "type": "string"}}, "type": "object"}, "ResourceInfo": {"description": "Proto that contains resource information.", "id": "ResourceInfo", "properties": {"chatAttachmentId": {"description": "Chat attachment ID.", "type": "string"}, "chatMessageId": {"description": "Chat message ID.", "type": "string"}, "deviceId": {"description": "Id to identify a device. For example, for Android devices, this is the \"Android Device Id\" and for Chrome OS devices, it's the \"Device Virtual Id\".", "type": "string"}, "documentId": {"description": "Drive file ID.", "type": "string"}, "resourceTitle": {"description": "Title of the resource, for example email subject, or document title.", "type": "string"}}, "type": "object"}, "RuleInfo": {"description": "Proto that contains rule information.", "id": "RuleInfo", "properties": {"displayName": {"description": "User provided name of the rule.", "type": "string"}, "resourceName": {"description": "Resource name that uniquely identifies the rule.", "type": "string"}}, "type": "object"}, "RuleViolationInfo": {"description": "Common alert information about violated rules that are configured by Google Workspace administrators.", "id": "RuleViolationInfo", "properties": {"dataSource": {"description": "Source of the data.", "enum": ["DATA_SOURCE_UNSPECIFIED", "DRIVE", "CHROME", "CHAT"], "enumDescriptions": ["Data source is unspecified.", "Drive data source.", "Chrome data source.", "Chat data source."], "type": "string"}, "eventType": {"description": "Event associated with this alert after applying the rule.", "enum": ["EVENT_TYPE_UNSPECIFIED", "ACCESS_BLOCKED", "SHARING_BLOCKED"], "enumDescriptions": ["Event type wasn't set.", "An access attempt was blocked.", "A sharing attempt was blocked."], "type": "string"}, "matchInfo": {"description": "List of matches that were found in the resource content.", "items": {"$ref": "MatchInfo"}, "type": "array"}, "recipients": {"description": "Resource recipients. For Drive, they are grantees that the Drive file was shared with at the time of rule triggering. Valid values include user emails, group emails, domains, or 'anyone' if the file was publicly accessible. If the file was private the recipients list will be empty. For Gmail, they are emails of the users or groups that the Gmail message was sent to.", "items": {"type": "string"}, "type": "array"}, "resourceInfo": {"$ref": "ResourceInfo", "description": "Details of the resource which violated the rule."}, "ruleInfo": {"$ref": "RuleInfo", "description": "Details of the violated rule."}, "suppressedActionTypes": {"description": "Actions suppressed due to other actions with higher priority.", "items": {"enum": ["ACTION_TYPE_UNSPECIFIED", "DRIVE_BLOCK_EXTERNAL_SHARING", "DRIVE_WARN_ON_EXTERNAL_SHARING", "DRIVE_RESTRICT_DOWNLOAD_PRINT_COPY", "DRIVE_APPLY_DRIVE_LABELS", "CHROME_BLOCK_FILE_DOWNLOAD", "CHROME_WARN_FILE_DOWNLOAD", "CHROME_BLOCK_FILE_UPLOAD", "CHROME_WARN_FILE_UPLOAD", "CHROME_BLOCK_WEB_CONTENT_UPLOAD", "CHROME_WARN_WEB_CONTENT_UPLOAD", "CHROME_BLOCK_PAGE_PRINT", "CHROME_WARN_PAGE_PRINT", "CHROME_BLOCK_URL_VISITED", "CHROME_WARN_URL_VISITED", "CHROME_BLOCK_SCREENSHOT", "CHROME_STORE_CONTENT", "DELETE_WEBPROTECT_EVIDENCE", "CHAT_BLOCK_CONTENT", "CHAT_WARN_USER", "ALERT", "RULE_ACTIVATE", "RULE_DEACTIVATE"], "enumDescriptions": ["Action type is unspecified.", "Block sharing a file externally.", "Show a warning message when sharing a file externally.", "Disable download, print, and copy for commenters and viewers in drive.", "Apply customer specified Drive labels to the file.", "Chrome actions. Block file download.", "Warn user about downloaded file.", "Block file upload.", "Warn user about uploaded file.", "Block web content upload.", "<PERSON>n user about uploaded web content.", "Block page print.", "Warn user about printed page.", "Block Chrome URL visit.", "Warn user about Chrome URL visited.", "Block screenshot alert.", "Store the content that violated the rule.", "Delete web protect evidence file", "Chat actions. Block Chat content to be sent out.", "Warn end user about Chat content.", "Send alert.", "Activate Rule Action", "Deactivate Rule Action"], "type": "string"}, "type": "array"}, "trigger": {"description": "Trigger of the rule.", "enum": ["TRIGGER_UNSPECIFIED", "DRIVE_SHARE", "CHROME_FILE_DOWNLOAD", "CHROME_FILE_UPLOAD", "CHROME_WEB_CONTENT_UPLOAD", "CHAT_MESSAGE_SENT", "CHAT_ATTACHMENT_UPLOADED", "CHROME_PAGE_PRINT", "CHROME_URL_VISITED"], "enumDescriptions": ["<PERSON>gger is unspecified.", "A Drive file is shared.", "A file being downloaded in a Chrome browser.", "A file being uploaded from a Chrome browser.", "Web content being uploaded from a Chrome browser.", "A Chat message is sent.", "A Chat attachment is uploaded.", "A page is being printed by Chrome.", "A URL is visited within Chrome."], "type": "string"}, "triggeredActionInfo": {"description": "Metadata related to the triggered actions.", "items": {"$ref": "ActionInfo"}, "type": "array"}, "triggeredActionTypes": {"description": "Actions applied as a consequence of the rule being triggered.", "items": {"enum": ["ACTION_TYPE_UNSPECIFIED", "DRIVE_BLOCK_EXTERNAL_SHARING", "DRIVE_WARN_ON_EXTERNAL_SHARING", "DRIVE_RESTRICT_DOWNLOAD_PRINT_COPY", "DRIVE_APPLY_DRIVE_LABELS", "CHROME_BLOCK_FILE_DOWNLOAD", "CHROME_WARN_FILE_DOWNLOAD", "CHROME_BLOCK_FILE_UPLOAD", "CHROME_WARN_FILE_UPLOAD", "CHROME_BLOCK_WEB_CONTENT_UPLOAD", "CHROME_WARN_WEB_CONTENT_UPLOAD", "CHROME_BLOCK_PAGE_PRINT", "CHROME_WARN_PAGE_PRINT", "CHROME_BLOCK_URL_VISITED", "CHROME_WARN_URL_VISITED", "CHROME_BLOCK_SCREENSHOT", "CHROME_STORE_CONTENT", "DELETE_WEBPROTECT_EVIDENCE", "CHAT_BLOCK_CONTENT", "CHAT_WARN_USER", "ALERT", "RULE_ACTIVATE", "RULE_DEACTIVATE"], "enumDescriptions": ["Action type is unspecified.", "Block sharing a file externally.", "Show a warning message when sharing a file externally.", "Disable download, print, and copy for commenters and viewers in drive.", "Apply customer specified Drive labels to the file.", "Chrome actions. Block file download.", "Warn user about downloaded file.", "Block file upload.", "Warn user about uploaded file.", "Block web content upload.", "<PERSON>n user about uploaded web content.", "Block page print.", "Warn user about printed page.", "Block Chrome URL visit.", "Warn user about Chrome URL visited.", "Block screenshot alert.", "Store the content that violated the rule.", "Delete web protect evidence file", "Chat actions. Block Chat content to be sent out.", "Warn end user about Chat content.", "Send alert.", "Activate Rule Action", "Deactivate Rule Action"], "type": "string"}, "type": "array"}, "triggeringUserEmail": {"description": "Email of the user who caused the violation. Value could be empty if not applicable, for example, a violation found by drive continuous scan.", "type": "string"}}, "type": "object"}, "SSOProfileCreatedEvent": {"description": "Event occurred when SSO Profile created in customer's account. The event are being received from insight forwarder", "id": "SSOProfileCreatedEvent", "properties": {"inboundSsoProfileName": {"description": "sso profile name which got created", "type": "string"}}, "type": "object"}, "SSOProfileDeletedEvent": {"description": "Event occurred when SSO Profile deleted in customer's account. The event are being received from insight forwarder", "id": "SSOProfileDeletedEvent", "properties": {"inboundSsoProfileName": {"description": "sso profile name which got deleted", "type": "string"}}, "type": "object"}, "SSOProfileUpdatedEvent": {"description": "Event occurred when SSO Profile updated in customer's account. The event are being received from insight forwarder", "id": "SSOProfileUpdatedEvent", "properties": {"inboundSsoProfileChanges": {"description": "changes made to sso profile", "type": "string"}, "inboundSsoProfileName": {"description": "sso profile name which got updated", "type": "string"}}, "type": "object"}, "SensitiveAdminAction": {"description": "Alert that is triggered when Sensitive Admin Action occur in customer account.", "id": "SensitiveAdminAction", "properties": {"actorEmail": {"description": "Email of person who performed the action", "type": "string"}, "eventTime": {"description": "The time at which event occurred", "format": "google-datetime", "type": "string"}, "primaryAdminChangedEvent": {"$ref": "PrimaryAdminChangedEvent", "description": "Event occurred when primary admin changed in customer's account"}, "ssoProfileCreatedEvent": {"$ref": "SSOProfileCreatedEvent", "description": "Event occurred when SSO Profile created in customer's account"}, "ssoProfileDeletedEvent": {"$ref": "SSOProfileDeletedEvent", "description": "Event occurred when SSO Profile deleted in customer's account"}, "ssoProfileUpdatedEvent": {"$ref": "SSOProfileUpdatedEvent", "description": "Event occurred when SSO Profile updated in customer's account"}, "superAdminPasswordResetEvent": {"$ref": "SuperAdminPasswordResetEvent", "description": "Event occurred when password was reset for super admin in customer's account"}}, "type": "object"}, "Settings": {"description": "Customer-level settings.", "id": "Settings", "properties": {"notifications": {"description": "The list of notifications.", "items": {"$ref": "Notification"}, "type": "array"}}, "type": "object"}, "StateSponsoredAttack": {"description": "A state-sponsored attack alert. Derived from audit logs.", "id": "StateSponsoredAttack", "properties": {"email": {"description": "The email of the user this incident was created for.", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "SuperAdminPasswordResetEvent": {"description": "Event occurred when password was reset for super admin in customer's account. The event are being received from insight forwarder", "id": "SuperAdminPasswordResetEvent", "properties": {"userEmail": {"description": "email of person whose password was reset", "type": "string"}}, "type": "object"}, "SupportTicket": {"description": "Support ticket related to Access Approvals request", "id": "SupportTicket", "properties": {"ticketId": {"description": "Support ticket ID", "type": "string"}, "ticketUrl": {"description": "Link to support ticket", "type": "string"}}, "type": "object"}, "SuspiciousActivity": {"description": "A mobile suspicious activity alert. Derived from audit logs.", "id": "SuspiciousActivity", "properties": {"email": {"description": "The email of the user this alert was created for.", "type": "string"}, "events": {"description": "Required. The list of security events.", "items": {"$ref": "SuspiciousActivitySecurityDetail"}, "type": "array"}}, "type": "object"}, "SuspiciousActivitySecurityDetail": {"description": "Detailed information of a single MDM suspicious activity event.", "id": "SuspiciousActivitySecurityDetail", "properties": {"deviceId": {"description": "Required. The device ID.", "type": "string"}, "deviceModel": {"description": "The model of the device.", "type": "string"}, "deviceProperty": {"description": "The device property which was changed.", "type": "string"}, "deviceType": {"description": "The type of the device.", "type": "string"}, "iosVendorId": {"description": "Required for iOS, empty for others.", "type": "string"}, "newValue": {"description": "The new value of the device property after the change.", "type": "string"}, "oldValue": {"description": "The old value of the device property before the change.", "type": "string"}, "resourceId": {"description": "The device resource ID.", "type": "string"}, "serialNumber": {"description": "The serial number of the device.", "type": "string"}}, "type": "object"}, "TransferError": {"description": "Details for an invalid transfer or forward.", "id": "TransferError", "properties": {"email": {"description": "User's email address. This may be unavailable if the entity was deleted.", "type": "string"}, "entityType": {"description": "Type of entity being transferred to. For ring group members, this should always be USER.", "enum": ["TRANSFER_ENTITY_TYPE_UNSPECIFIED", "TRANSFER_AUTO_ATTENDANT", "TRANSFER_RING_GROUP", "TRANSFER_USER"], "enumDescriptions": ["Entity type wasn't set.", "Transfer to auto attendant.", "Transfer to ring group.", "Transfer to user."], "type": "string"}, "id": {"description": "Ring group or auto attendant ID. Not set for users.", "type": "string"}, "invalidReason": {"description": "Reason for the error.", "enum": ["TRANSFER_INVALID_REASON_UNSPECIFIED", "TRANSFER_TARGET_DELETED", "UNLICENSED", "SUSPENDED", "NO_PHONE_NUMBER"], "enumDescriptions": ["Reason wasn't specified.", "The transfer target can't be found—most likely it was deleted.", "The user's Google Voice license was removed.", "The user's Google Workspace account was suspended.", "The transfer target no longer has a phone number. This reason should become deprecated once we support numberless transfer."], "type": "string"}, "name": {"description": "User's full name, or the ring group / auto attendant name. This may be unavailable if the entity was deleted.", "type": "string"}}, "type": "object"}, "TransferMisconfiguration": {"description": "Error related to transferring or forwarding a phone call.", "id": "TransferMisconfiguration", "properties": {"errors": {"description": "Details for each invalid transfer or forward.", "items": {"$ref": "TransferError"}, "type": "array"}}, "type": "object"}, "UndeleteAlertRequest": {"description": "A request to undelete a specific alert that was marked for deletion.", "id": "UndeleteAlertRequest", "properties": {"customerId": {"description": "Optional. The unique identifier of the Google Workspace account of the customer the alert is associated with. The `customer_id` must have the initial \"C\" stripped (for example, `046psxkn`). Inferred from the caller identity if not provided. [Find your customer ID](https://support.google.com/cloudidentity/answer/********).", "type": "string"}}, "type": "object"}, "User": {"description": "A user.", "id": "User", "properties": {"displayName": {"description": "Display name of the user.", "type": "string"}, "emailAddress": {"description": "Email address of the user.", "type": "string"}}, "type": "object"}, "UserChanges": {"description": "Alerts from UserChanges bucket Rules for predefined rules which contain the following rules: - Suspended user made active - New user added - User suspended (by admin) - User granted admin privileges - User admin privileges revoked - User deleted - Users password changed", "id": "UserChanges", "properties": {"name": {"description": "Rule name", "type": "string"}}, "type": "object"}, "UserDefinedDetectorInfo": {"description": "Detector defined by administrators.", "id": "UserDefinedDetectorInfo", "properties": {"displayName": {"description": "Display name of the detector.", "type": "string"}, "resourceName": {"description": "Resource name that uniquely identifies the detector.", "type": "string"}}, "type": "object"}, "VaultAcceleratedDeletion": {"description": "Alert that is triggered when a Vault accelerated deletion request is created or canceled.", "id": "VaultAcceleratedDeletion", "properties": {"actionType": {"description": "The action can be one of create and cancel", "enum": ["VAULT_ACCELERATED_DELETION_ACTION_TYPE_UNSPECIFIED", "VAULT_ACCELERATED_DELETION_ACTION_TYPE_CREATE", "VAULT_ACCELERATED_DELETION_ACTION_TYPE_CANCEL"], "enumDescriptions": ["Unspecified action type", "AD Create action type", "AD Cancel action type"], "type": "string"}, "appType": {"description": "Currentlty only Gmail is supported as app type", "enum": ["VAULT_ACCELERATED_DELETION_APP_TYPE_UNSPECIFIED", "VAULT_ACCELERATED_DELETION_APP_TYPE_GMAIL"], "enumDescriptions": ["Unspecified app type", "Gmail app type"], "type": "string"}, "createTime": {"description": "The UTC timestamp of when the AD request was created", "format": "google-datetime", "type": "string"}, "deletionRequestId": {"description": "Accelerated deletion request ID intended to be used to construct the Vault UI link to the AD request", "type": "string"}, "matterId": {"description": "Matter ID of the accelerated deletion request intended to be used to construct the Vault UI link to the AD request", "type": "string"}}, "type": "object"}, "VoiceMisconfiguration": {"description": "An alert triggered when Google Voice configuration becomes invalid, generally due to an external entity being modified or deleted.", "id": "VoiceMisconfiguration", "properties": {"entityName": {"description": "Name of the entity whose configuration is now invalid.", "type": "string"}, "entityType": {"description": "Type of the entity whose configuration is now invalid.", "enum": ["ENTITY_TYPE_UNSPECIFIED", "AUTO_ATTENDANT", "RING_GROUP"], "enumDescriptions": ["Entity type wasn't set.", "Invalid auto attendant.", "Invalid ring group."], "type": "string"}, "fixUri": {"description": "Link that the admin can follow to fix the issue.", "type": "string"}, "membersMisconfiguration": {"$ref": "TransferMisconfiguration", "description": "Issue(s) with members of a ring group."}, "transferMisconfiguration": {"$ref": "TransferMisconfiguration", "description": "Issue(s) with transferring or forwarding to an external entity."}, "voicemailMisconfiguration": {"$ref": "VoicemailMisconfiguration", "description": "Issue(s) with sending to voicemail."}}, "type": "object"}, "VoicemailMisconfiguration": {"description": "Issue(s) with sending to voicemail.", "id": "VoicemailMisconfiguration", "properties": {"errors": {"description": "Issue(s) with voicemail recipients.", "items": {"$ref": "VoicemailRecipientError"}, "type": "array"}}, "type": "object"}, "VoicemailRecipientError": {"description": "Issue(s) with a voicemail recipient.", "id": "VoicemailRecipientError", "properties": {"email": {"description": "Email address of the invalid recipient. This may be unavailable if the recipient was deleted.", "type": "string"}, "invalidReason": {"description": "Reason for the error.", "enum": ["EMAIL_INVALID_REASON_UNSPECIFIED", "OUT_OF_QUOTA", "RECIPIENT_DELETED"], "enumDescriptions": ["Reason wasn't specified.", "User can't receive emails due to insufficient quota.", "All recipients were deleted."], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Google Workspace Alert Center API", "version": "v1beta1", "version_module": true}