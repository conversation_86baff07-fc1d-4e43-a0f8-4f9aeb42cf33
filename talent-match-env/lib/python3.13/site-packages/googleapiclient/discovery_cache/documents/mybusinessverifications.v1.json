{"basePath": "", "baseUrl": "https://mybusinessverifications.googleapis.com/", "batchPath": "batch", "canonicalName": "My Business Verifications", "description": "The My Business Verifications API provides an interface for taking verifications related actions for locations.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/my-business/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "mybusinessverifications:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://mybusinessverifications.mtls.googleapis.com/", "name": "mybusinessverifications", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"locations": {"methods": {"fetchVerificationOptions": {"description": "Reports all eligible verification options for a location in a specific language.", "flatPath": "v1/locations/{locationsId}:fetchVerificationOptions", "httpMethod": "POST", "id": "mybusinessverifications.locations.fetchVerificationOptions", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location to verify.", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:fetchVerificationOptions", "request": {"$ref": "FetchVerificationOptionsRequest"}, "response": {"$ref": "FetchVerificationOptionsResponse"}}, "getVoiceOfMerchantState": {"description": "Gets the VoiceOfMerchant state.", "flatPath": "v1/locations/{locationsId}/VoiceOfMerchantState", "httpMethod": "GET", "id": "mybusinessverifications.locations.getVoiceOfMerchantState", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the location.", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/VoiceOfMerchantState", "response": {"$ref": "VoiceOfMerchantState"}}, "verify": {"description": "Starts the verification process for a location.", "flatPath": "v1/locations/{locationsId}:verify", "httpMethod": "POST", "id": "mybusinessverifications.locations.verify", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the location to verify.", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:verify", "request": {"$ref": "VerifyLocationRequest"}, "response": {"$ref": "VerifyLocationResponse"}}}, "resources": {"verifications": {"methods": {"complete": {"description": "Completes a `PENDING` verification. It is only necessary for non `AUTO` verification methods. `AUTO` verification request is instantly `VERIFIED` upon creation.", "flatPath": "v1/locations/{locationsId}/verifications/{verificationsId}:complete", "httpMethod": "POST", "id": "mybusinessverifications.locations.verifications.complete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the verification to complete.", "location": "path", "pattern": "^locations/[^/]+/verifications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:complete", "request": {"$ref": "CompleteVerificationRequest"}, "response": {"$ref": "CompleteVerificationResponse"}}, "list": {"description": "List verifications of a location, ordered by create time.", "flatPath": "v1/locations/{locationsId}/verifications", "httpMethod": "GET", "id": "mybusinessverifications.locations.verifications.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "How many verification to include per page. Minimum is 1, and the default and maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If specified, returns the next page of verifications.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the location that verification requests belong to.", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/verifications", "response": {"$ref": "ListVerificationsResponse"}}}}}}}, "revision": "20250615", "rootUrl": "https://mybusinessverifications.googleapis.com/", "schemas": {"AddressVerificationData": {"description": "Display data for verifications through postcard.", "id": "AddressVerificationData", "properties": {"address": {"$ref": "PostalAddress", "description": "Address that a postcard can be sent to."}, "business": {"description": "Merchant's business name.", "type": "string"}, "expectedDeliveryDaysRegion": {"description": "Expected number of days it takes to deliver a postcard to the address's region.", "format": "int32", "type": "integer"}}, "type": "object"}, "CompleteVerificationRequest": {"description": "Request message for Verifications.CompleteVerificationAction.", "id": "CompleteVerificationRequest", "properties": {"pin": {"description": "Required. PIN code received by the merchant to complete the verification.", "type": "string"}}, "type": "object"}, "CompleteVerificationResponse": {"description": "Response message for Verifications.CompleteVerificationAction.", "id": "CompleteVerificationResponse", "properties": {"verification": {"$ref": "Verification", "description": "The completed verification."}}, "type": "object"}, "ComplyWithGuidelines": {"description": "Indicates that the location fails to comply with our [guidelines](https://support.google.com/business/answer/3038177).", "id": "ComplyWithGuidelines", "properties": {"recommendationReason": {"description": "The reason why the location is being recommended to comply with guidelines.", "enum": ["RECOMMENDATION_REASON_UNSPECIFIED", "BUSINESS_LOCATION_SUSPENDED", "BUSINESS_LOCATION_DISABLED"], "enumDescriptions": ["Not specified.", "The business location is suspended. To fix this issue, consult the [Help Center article](https://support.google.com/business/answer/4569145).", "The business location is disabled. To fix this issue, consult the [Help Center article](https://support.google.com/business/answer/9334246)."], "type": "string"}}, "type": "object"}, "EmailVerificationData": {"description": "Display data for verifications through email.", "id": "EmailVerificationData", "properties": {"domain": {"description": "Domain name in the email address. e.g. \"gmail.com\" in <EMAIL>", "type": "string"}, "isUserNameEditable": {"description": "Whether client is allowed to provide a different user name.", "type": "boolean"}, "user": {"description": "User name in the email address. e.g. \"foo\" in <EMAIL>", "type": "string"}}, "type": "object"}, "FetchVerificationOptionsRequest": {"description": "Request message for Verifications.FetchVerificationOptions.", "id": "FetchVerificationOptionsRequest", "properties": {"context": {"$ref": "ServiceBusinessContext", "description": "Optional. Extra context information for the verification of service businesses. Can only be applied to the locations whose business type is CUSTOMER_LOCATION_ONLY. Specifying an accurate address could enable more options. INVALID_ARGUMENT will be thrown if it is set for other business types of locations."}, "languageCode": {"description": "Required. The BCP 47 language code representing the language that is to be used for the verification process. Available options vary by language.", "type": "string"}}, "type": "object"}, "FetchVerificationOptionsResponse": {"description": "Response message for Verifications.FetchVerificationOptions.", "id": "FetchVerificationOptionsResponse", "properties": {"options": {"description": "The available verification options.", "items": {"$ref": "VerificationOption"}, "type": "array"}}, "type": "object"}, "ListVerificationsResponse": {"description": "Response message for Verifications.ListVerifications.", "id": "ListVerificationsResponse", "properties": {"nextPageToken": {"description": "If the number of verifications exceeded the requested page size, this field will be populated with a token to fetch the next page of verification on a subsequent call. If there are no more attributes, this field will not be present in the response.", "type": "string"}, "verifications": {"description": "List of the verifications.", "items": {"$ref": "Verification"}, "type": "array"}}, "type": "object"}, "PostalAddress": {"description": "Represents a postal address, such as for postal delivery or payments addresses. With a postal address, a postal service can deliver items to a premise, P.O. box, or similar. A postal address is not intended to model geographical locations like roads, towns, or mountains. In typical usage, an address would be created by user input or from importing existing data, depending on the type of process. Advice on address input or editing: - Use an internationalization-ready address widget such as https://github.com/google/libaddressinput. - Users should not be presented with UI elements for input or editing of fields outside countries where that field is used. For more guidance on how to use this schema, see: https://support.google.com/business/answer/6397478.", "id": "PostalAddress", "properties": {"addressLines": {"description": "Unstructured address lines describing the lower levels of an address. Because values in `address_lines` do not have type information and may sometimes contain multiple values in a single field (for example, \"Austin, TX\"), it is important that the line order is clear. The order of address lines should be \"envelope order\" for the country or region of the address. In places where this can vary (for example, Japan), `address_language` is used to make it explicit (for example, \"ja\" for large-to-small ordering and \"ja-Latn\" or \"en\" for small-to-large). In this way, the most specific line of an address can be selected based on the language. The minimum permitted structural representation of an address consists of a `region_code` with all remaining information placed in the `address_lines`. It would be possible to format such an address very approximately without geocoding, but no semantic reasoning could be made about any of the address components until it was at least partially resolved. Creating an address only containing a `region_code` and `address_lines` and then geocoding is the recommended way to handle completely unstructured addresses (as opposed to guessing which parts of the address should be localities or administrative areas).", "items": {"type": "string"}, "type": "array"}, "administrativeArea": {"description": "Optional. Highest administrative subdivision which is used for postal addresses of a country or region. For example, this can be a state, a province, an oblast, or a prefecture. For Spain, this is the province and not the autonomous community (for example, \"Barcelona\" and not \"Catalonia\"). Many countries don't use an administrative area in postal addresses. For example, in Switzerland, this should be left unpopulated.", "type": "string"}, "languageCode": {"description": "Optional. BCP-47 language code of the contents of this address (if known). This is often the UI language of the input form or is expected to match one of the languages used in the address' country/region, or their transliterated equivalents. This can affect formatting in certain countries, but is not critical to the correctness of the data and will never affect any validation or other non-formatting related operations. If this value is not known, it should be omitted (rather than specifying a possibly incorrect default). Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".", "type": "string"}, "locality": {"description": "Optional. Generally refers to the city or town portion of the address. Examples: US city, IT comune, UK post town. In regions of the world where localities are not well defined or do not fit into this structure well, leave `locality` empty and use `address_lines`.", "type": "string"}, "organization": {"description": "Optional. The name of the organization at the address.", "type": "string"}, "postalCode": {"description": "Optional. Postal code of the address. Not all countries use or require postal codes to be present, but where they are used, they may trigger additional validation with other parts of the address (for example, state or zip code validation in the United States).", "type": "string"}, "recipients": {"description": "Optional. The recipient at the address. This field may, under certain circumstances, contain multiline information. For example, it might contain \"care of\" information.", "items": {"type": "string"}, "type": "array"}, "regionCode": {"description": "Required. CLDR region code of the country/region of the address. This is never inferred and it is up to the user to ensure the value is correct. See https://cldr.unicode.org/ and https://www.unicode.org/cldr/charts/30/supplemental/territory_information.html for details. Example: \"CH\" for Switzerland.", "type": "string"}, "revision": {"description": "The schema revision of the `PostalAddress`. This must be set to 0, which is the latest revision. All new revisions **must** be backward compatible with old revisions.", "format": "int32", "type": "integer"}, "sortingCode": {"description": "Optional. Additional, country-specific, sorting code. This is not used in most regions. Where it is used, the value is either a string like \"CEDEX\", optionally followed by a number (for example, \"CEDEX 7\"), or just a number alone, representing the \"sector code\" (Jamaica), \"delivery area indicator\" (Malawi) or \"post office indicator\" (Côte d'Ivoire).", "type": "string"}, "sublocality": {"description": "Optional. Sublocality of the address. For example, this can be a neighborhood, borough, or district.", "type": "string"}}, "type": "object"}, "ResolveOwnershipConflict": {"description": "Indicates that the location duplicates another location that is in good standing.", "id": "ResolveOwnershipConflict", "properties": {}, "type": "object"}, "ServiceBusinessContext": {"description": "Additional data for service business verification.", "id": "ServiceBusinessContext", "properties": {"address": {"$ref": "PostalAddress", "description": "The verification address of the location. It is used to either enable more verification options or send a postcard."}}, "type": "object"}, "Verification": {"description": "A verification represents a verification attempt on a location.", "id": "Verification", "properties": {"announcement": {"description": "Optional. Response announcement set only if the method is VETTED_PARTNER.", "type": "string"}, "createTime": {"description": "The timestamp when the verification is requested.", "format": "google-datetime", "type": "string"}, "method": {"description": "The method of the verification.", "enum": ["VERIFICATION_METHOD_UNSPECIFIED", "ADDRESS", "EMAIL", "PHONE_CALL", "SMS", "AUTO", "VETTED_PARTNER"], "enumDescriptions": ["Default value, will result in errors.", "Send a postcard with a verification PIN to a specific mailing address. The PIN is used to complete verification with Google.", "Send an email with a verification PIN to a specific email address. The PIN is used to complete verification with Google.", "Make a phone call with a verification PIN to a specific phone number. The PIN is used to complete verification with Google.", "Send an SMS with a verification PIN to a specific phone number. The PIN is used to complete verification with Google.", "Verify the location without additional user action. This option may not be available for all locations.", "This option may not be available for all locations."], "type": "string"}, "name": {"description": "Resource name of the verification.", "type": "string"}, "state": {"description": "The state of the verification.", "enum": ["STATE_UNSPECIFIED", "PENDING", "COMPLETED", "FAILED"], "enumDescriptions": ["Default value, will result in errors.", "The verification is pending.", "The verification is completed.", "The verification is failed."], "type": "string"}}, "type": "object"}, "VerificationOption": {"description": "The verification option represents how to verify the location (indicated by verification method) and where the verification will be sent to (indicated by display data).", "id": "VerificationOption", "properties": {"addressData": {"$ref": "AddressVerificationData", "description": "Set only if the method is MAIL."}, "announcement": {"description": "Set only if the method is VETTED_PARTNER.", "type": "string"}, "emailData": {"$ref": "EmailVerificationData", "description": "Set only if the method is EMAIL."}, "phoneNumber": {"description": "Set only if the method is PHONE_CALL or SMS. Phone number that the PIN will be sent to.", "type": "string"}, "verificationMethod": {"description": "Method to verify the location.", "enum": ["VERIFICATION_METHOD_UNSPECIFIED", "ADDRESS", "EMAIL", "PHONE_CALL", "SMS", "AUTO", "VETTED_PARTNER"], "enumDescriptions": ["Default value, will result in errors.", "Send a postcard with a verification PIN to a specific mailing address. The PIN is used to complete verification with Google.", "Send an email with a verification PIN to a specific email address. The PIN is used to complete verification with Google.", "Make a phone call with a verification PIN to a specific phone number. The PIN is used to complete verification with Google.", "Send an SMS with a verification PIN to a specific phone number. The PIN is used to complete verification with Google.", "Verify the location without additional user action. This option may not be available for all locations.", "This option may not be available for all locations."], "type": "string"}}, "type": "object"}, "VerificationToken": {"description": "Token generated by a vetted [partner](https://support.google.com/business/answer/7674102).", "id": "VerificationToken", "properties": {"tokenString": {"description": "The token string.", "type": "string"}}, "type": "object"}, "Verify": {"description": "Indicates that the location requires verification. Contains information about the current verification actions performed on the location.", "id": "Verify", "properties": {"hasPendingVerification": {"description": "Indicates whether a verification process has already started, and can be completed by the location.", "type": "boolean"}}, "type": "object"}, "VerifyLocationRequest": {"description": "Request message for Verifications.VerifyLocation.", "id": "VerifyLocationRequest", "properties": {"context": {"$ref": "ServiceBusinessContext", "description": "Optional. Extra context information for the verification of service businesses. It is only required for the locations whose business type is CUSTOMER_LOCATION_ONLY. For ADDRESS verification, the address will be used to send out postcard. For other methods, it should be the same as the one that is passed to GetVerificationOptions. INVALID_ARGUMENT will be thrown if it is set for other types of business locations."}, "emailAddress": {"description": "Optional. The input for EMAIL method. Email address where the PIN should be sent to. An email address is accepted only if it is one of the addresses provided by FetchVerificationOptions. If the EmailVerificationData has is_user_name_editable set to true, the client may specify a different user name (local-part) but must match the domain name.", "type": "string"}, "languageCode": {"description": "Optional. The BCP 47 language code representing the language that is to be used for the verification process.", "type": "string"}, "mailerContact": {"description": "Optional. The input for ADDRESS method. Contact name the mail should be sent to.", "type": "string"}, "method": {"description": "Required. Verification method.", "enum": ["VERIFICATION_METHOD_UNSPECIFIED", "ADDRESS", "EMAIL", "PHONE_CALL", "SMS", "AUTO", "VETTED_PARTNER"], "enumDescriptions": ["Default value, will result in errors.", "Send a postcard with a verification PIN to a specific mailing address. The PIN is used to complete verification with Google.", "Send an email with a verification PIN to a specific email address. The PIN is used to complete verification with Google.", "Make a phone call with a verification PIN to a specific phone number. The PIN is used to complete verification with Google.", "Send an SMS with a verification PIN to a specific phone number. The PIN is used to complete verification with Google.", "Verify the location without additional user action. This option may not be available for all locations.", "This option may not be available for all locations."], "type": "string"}, "phoneNumber": {"description": "Optional. The input for PHONE_CALL/SMS method The phone number that should be called or be sent SMS to. It must be one of the phone numbers in the eligible options.", "type": "string"}, "token": {"$ref": "VerificationToken", "description": "Optional. The input for VETTED_PARTNER method available to select [partners.](https://support.google.com/business/answer/7674102) The input is not needed for a vetted account. Token that is associated to the location. Token that is associated to the location."}}, "type": "object"}, "VerifyLocationResponse": {"description": "Response message for Verifications.VerifyLocation.", "id": "VerifyLocationResponse", "properties": {"verification": {"$ref": "Verification", "description": "The created verification request."}}, "type": "object"}, "VoiceOfMerchantState": {"description": "Response message for VoiceOfMerchant.GetVoiceOfMerchantState.", "id": "VoiceOfMerchantState", "properties": {"complyWithGuidelines": {"$ref": "ComplyWithGuidelines", "description": "The location fails to comply with our [guidelines](https://support.google.com/business/answer/3038177) and requires additional steps for reinstatement. To fix this issue, consult the [Help Center Article](https://support.google.com/business/answer/4569145)."}, "hasBusinessAuthority": {"description": "Indicates whether the location has the authority (ownership) over the business on Google. If true, another location cannot take over and become the dominant listing on Maps. However, edits will not become live unless Voice of Merchant is gained (i.e. has_voice_of_merchant is true).", "type": "boolean"}, "hasVoiceOfMerchant": {"description": "Indicates whether the location is in good standing and has control over the business on Google. Any edits made to the location will propagate to Maps after passing the review phase.", "type": "boolean"}, "resolveOwnershipConflict": {"$ref": "ResolveOwnershipConflict", "description": "This location duplicates another location that is in good standing. If you have access to the location in good standing, use that location's id to perform operations. Otherwise, request access from the current owner."}, "verify": {"$ref": "Verify", "description": "Start or continue the verification process."}, "waitForVoiceOfMerchant": {"$ref": "WaitForVoiceOfMerchant", "description": "Wait to gain Voice of Merchant. The location is under review for quality purposes."}}, "type": "object"}, "WaitForVoiceOfMerchant": {"description": "Indicates that the location will gain voice of merchant after passing review.", "id": "WaitForVoiceOfMerchant", "properties": {}, "type": "object"}}, "servicePath": "", "title": "My Business Verifications API", "version": "v1", "version_module": true}