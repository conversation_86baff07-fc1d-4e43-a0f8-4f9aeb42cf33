#!/usr/bin/env python3
"""
Gemini AI-powered Talent Matching System v2.0

Refactored version with comprehensive UI support, new data structure,
and advanced matching capabilities including OKR framework and skill proficiency scoring.
"""

import asyncio
import os
import sys
import json
import logging
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import math

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

# LightRAG imports
from lightrag import LightRAG, QueryParam
from lightrag.utils import logger, EmbeddingFunc

# Gemini adapter imports
from gemini_lightrag_adapter import gemini_4o_mini_complete, gemini_embed

# Standard library imports
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

@dataclass
class SkillProficiency:
    """Data class for skill proficiency assessment."""
    skill: str
    proficiency: float  # 0-100 percentage
    years_experience: int
    level: str  # Beginner, Intermediate, Advanced, Expert

@dataclass
class OKRObjective:
    """Data class for OKR (Objectives and Key Results) framework."""
    objective: str
    key_results: List[Dict[str, Any]]
    progress: float  # 0-100 percentage
    target_date: str
    status: str  # Not Started, In Progress, Completed, At Risk

@dataclass
class TalentProfile:
    """Enhanced data class for talent profile information."""
    id: str
    name: str
    title: str
    email: str
    phone: str
    location: str
    linkedin: str
    github: str
    
    # Professional information
    current_company: str
    current_role: str
    employment_status: str
    availability: str
    salary_expectation: str
    remote_work: bool
    
    # Skills and experience
    skills: List[SkillProficiency]
    experience_years: int
    previous_companies: List[str]
    education: str
    certifications: List[str]
    achievements: List[str]
    
    # Career planning
    okr_objectives: List[OKRObjective]
    career_goals: List[str]
    project_interests: List[str]
    preferred_work_environment: str
    
    # Metadata
    raw_content: str
    created_at: datetime = None
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_updated is None:
            self.last_updated = datetime.now()

@dataclass
class CandidateProfile:
    """Data class for candidate resume information."""
    id: str
    name: str
    email: str
    phone: str
    location: str
    linkedin: str
    github: str
    portfolio: str
    
    # Application information
    application_date: datetime
    status: str  # New Application, Under Review, Interview, Rejected, Hired
    interested_projects: List[str]
    preferred_role: str
    salary_expectation: str
    remote_work: bool
    
    # Skills and experience
    skills: List[SkillProficiency]
    experience_years: int
    education: str
    certifications: List[str]
    projects: List[Dict[str, Any]]
    
    # Matching data
    skill_radar_data: Dict[str, float]  # For radar chart visualization
    project_matches: Dict[str, float]  # Project ID -> Match score
    
    # Metadata
    raw_content: str
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ProjectRequirement:
    """Enhanced data class for project requirement information."""
    id: str
    name: str
    company: str
    department: str
    project_manager: str
    
    # Project details
    start_date: datetime
    end_date: datetime
    status: str  # Planning, Active, On Hold, Completed
    priority: str  # Low, Medium, High, Critical
    budget: str
    
    # Technical information
    description: str
    business_objectives: List[str]
    technical_requirements: List[str]
    technical_stack: List[str]
    
    # Team information
    current_team: List[Dict[str, Any]]
    open_positions: List[Dict[str, Any]]
    
    # Requirements
    required_skills: List[str]
    experience_level: str
    timeline: str
    location_preference: str
    
    # Metadata
    raw_content: str
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class GeminiTalentMatcherV2:
    """
    Advanced talent matching system using LightRAG with Gemini AI v2.0.
    
    This class provides comprehensive talent matching capabilities with
    enhanced UI support, OKR framework, and advanced scoring algorithms.
    """
    
    def __init__(
        self,
        working_dir: str = "./gemini_lightrag_v2_cache",
        talent_dir: str = "./talent",
        resume_dir: str = "./resume", 
        projects_dir: str = "./projects",
        api_key: Optional[str] = None,
        log_level: str = "INFO"
    ):
        """
        Initialize the Gemini Talent Matcher v2.0.
        
        Args:
            working_dir: Directory for LightRAG cache and storage
            talent_dir: Directory containing established talent profiles
            resume_dir: Directory containing candidate resumes
            projects_dir: Directory containing project specifications
            api_key: Google API key (if not set in environment)
            log_level: Logging level
        """
        self.working_dir = Path(working_dir)
        self.talent_dir = Path(talent_dir)
        self.resume_dir = Path(resume_dir)
        self.projects_dir = Path(projects_dir)
        
        # Create directories if they don't exist
        for directory in [self.working_dir, self.talent_dir, self.resume_dir, self.projects_dir]:
            directory.mkdir(exist_ok=True)
        
        # Set up API key
        if api_key:
            os.environ["GOOGLE_API_KEY"] = api_key
        elif not os.getenv("GOOGLE_API_KEY"):
            logger.warning("No Google API key found. Please set GOOGLE_API_KEY environment variable.")
        
        # Configure logging
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Initialize LightRAG with Gemini functions
        self.rag = LightRAG(
            working_dir=str(self.working_dir),
            llm_model_func=gemini_4o_mini_complete,
            embedding_func=EmbeddingFunc(
                embedding_dim=768,  # Gemini text-embedding-004 dimension
                max_token_size=8192,
                func=gemini_embed,
            ),
        )
        
        # Storage for structured data
        self.talent_profiles: Dict[str, TalentProfile] = {}
        self.candidate_profiles: Dict[str, CandidateProfile] = {}
        self.project_requirements: Dict[str, ProjectRequirement] = {}
        
        logger.info(f"Gemini Talent Matcher v2.0 initialized")
        logger.info(f"Directories - Working: {self.working_dir}, Talent: {self.talent_dir}, Resume: {self.resume_dir}, Projects: {self.projects_dir}")
    
    def load_all_data(self) -> Dict[str, int]:
        """
        Load all data from the directory structure.
        
        Returns:
            Dict with counts of loaded items
        """
        stats = {
            "talents_loaded": 0,
            "candidates_loaded": 0,
            "projects_loaded": 0,
            "errors": 0
        }
        
        # Load talent profiles
        for file_path in self.talent_dir.glob("*.txt"):
            try:
                profile = self._load_talent_profile(file_path)
                if profile:
                    self.talent_profiles[profile.id] = profile
                    stats["talents_loaded"] += 1
            except Exception as e:
                logger.error(f"Error loading talent profile {file_path}: {e}")
                stats["errors"] += 1
        
        # Load candidate resumes
        for file_path in self.resume_dir.glob("*.txt"):
            try:
                candidate = self._load_candidate_profile(file_path)
                if candidate:
                    self.candidate_profiles[candidate.id] = candidate
                    stats["candidates_loaded"] += 1
            except Exception as e:
                logger.error(f"Error loading candidate resume {file_path}: {e}")
                stats["errors"] += 1
        
        # Load project requirements
        for file_path in self.projects_dir.glob("*.txt"):
            try:
                project = self._load_project_requirement(file_path)
                if project:
                    self.project_requirements[project.id] = project
                    stats["projects_loaded"] += 1
            except Exception as e:
                logger.error(f"Error loading project requirement {file_path}: {e}")
                stats["errors"] += 1
        
        logger.info(f"Data loading complete: {stats}")
        return stats
    
    def _load_talent_profile(self, file_path: Path) -> Optional[TalentProfile]:
        """Load talent profile from text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract basic information using regex patterns
            name = self._extract_field(content, r"Name:\s*(.+)")
            title = self._extract_field(content, r"Title:\s*(.+)")
            email = self._extract_field(content, r"Email:\s*(.+)")
            
            # Generate ID from filename
            profile_id = file_path.stem
            
            # Parse skills with proficiency
            skills = self._parse_skills_from_content(content)
            
            # Parse OKR objectives
            okr_objectives = self._parse_okr_objectives(content)
            
            # Create profile object
            profile = TalentProfile(
                id=profile_id,
                name=name or "Unknown",
                title=title or "Unknown",
                email=email or "",
                phone=self._extract_field(content, r"Phone:\s*(.+)") or "",
                location=self._extract_field(content, r"Location:\s*(.+)") or "",
                linkedin=self._extract_field(content, r"LinkedIn:\s*(.+)") or "",
                github=self._extract_field(content, r"GitHub:\s*(.+)") or "",
                current_company=self._extract_field(content, r"Current Company:\s*(.+)") or "",
                current_role=self._extract_field(content, r"Current Role:\s*(.+)") or "",
                employment_status=self._extract_field(content, r"Employment Status:\s*(.+)") or "",
                availability=self._extract_field(content, r"Availability:\s*(.+)") or "",
                salary_expectation=self._extract_field(content, r"Salary Expectation:\s*(.+)") or "",
                remote_work=self._extract_boolean_field(content, r"Remote Work:\s*(.+)"),
                skills=skills,
                experience_years=self._extract_years_experience(content),
                previous_companies=self._extract_list_field(content, r"Previous Companies?:\s*(.+)"),
                education=self._extract_field(content, r"Education:\s*(.+)") or "",
                certifications=self._extract_certifications(content),
                achievements=self._extract_achievements(content),
                okr_objectives=okr_objectives,
                career_goals=self._extract_career_goals(content),
                project_interests=self._extract_project_interests(content),
                preferred_work_environment=self._extract_field(content, r"Preferred Work Environment:\s*(.+)") or "",
                raw_content=content
            )
            
            return profile
            
        except Exception as e:
            logger.error(f"Error parsing talent profile {file_path}: {e}")
            return None
    
    def _load_candidate_profile(self, file_path: Path) -> Optional[CandidateProfile]:
        """Load candidate profile from text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract basic information
            name = self._extract_field(content, r"Name:\s*(.+)")
            email = self._extract_field(content, r"Email:\s*(.+)")
            
            # Generate ID from filename
            candidate_id = file_path.stem
            
            # Parse skills with proficiency
            skills = self._parse_skills_from_content(content)
            
            # Generate skill radar data
            skill_radar_data = self._generate_skill_radar_data(skills, content)
            
            # Calculate project matches (will be updated when projects are loaded)
            project_matches = {}
            
            # Create candidate object
            candidate = CandidateProfile(
                id=candidate_id,
                name=name or "Unknown",
                email=email or "",
                phone=self._extract_field(content, r"Phone:\s*(.+)") or "",
                location=self._extract_field(content, r"Location:\s*(.+)") or "",
                linkedin=self._extract_field(content, r"LinkedIn:\s*(.+)") or "",
                github=self._extract_field(content, r"GitHub:\s*(.+)") or "",
                portfolio=self._extract_field(content, r"Portfolio:\s*(.+)") or "",
                application_date=self._extract_application_date(content),
                status=self._extract_field(content, r"Status:\s*(.+)") or "New Application",
                interested_projects=self._extract_interested_projects(content),
                preferred_role=self._extract_field(content, r"Preferred Role:\s*(.+)") or "",
                salary_expectation=self._extract_field(content, r"Salary Expectation:\s*(.+)") or "",
                remote_work=self._extract_boolean_field(content, r"Remote Work:\s*(.+)"),
                skills=skills,
                experience_years=self._extract_years_experience(content),
                education=self._extract_education(content),
                certifications=self._extract_certifications(content),
                projects=self._extract_projects(content),
                skill_radar_data=skill_radar_data,
                project_matches=project_matches,
                raw_content=content
            )
            
            return candidate
            
        except Exception as e:
            logger.error(f"Error parsing candidate profile {file_path}: {e}")
            return None
    
    def _load_project_requirement(self, file_path: Path) -> Optional[ProjectRequirement]:
        """Load project requirement from text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract basic information
            project_id = self._extract_field(content, r"Project ID:\s*(.+)") or file_path.stem
            name = self._extract_field(content, r"Project Name:\s*(.+)")
            company = self._extract_field(content, r"Company:\s*(.+)")
            
            # Create project object
            project = ProjectRequirement(
                id=project_id,
                name=name or "Unknown Project",
                company=company or "",
                department=self._extract_field(content, r"Department:\s*(.+)") or "",
                project_manager=self._extract_field(content, r"Project Manager:\s*(.+)") or "",
                start_date=self._extract_date_field(content, r"Start Date:\s*(.+)"),
                end_date=self._extract_date_field(content, r"End Date:\s*(.+)"),
                status=self._extract_field(content, r"Status:\s*(.+)") or "Planning",
                priority=self._extract_field(content, r"Priority:\s*(.+)") or "Medium",
                budget=self._extract_field(content, r"Budget:\s*(.+)") or "",
                description=self._extract_description(content),
                business_objectives=self._extract_business_objectives(content),
                technical_requirements=self._extract_technical_requirements(content),
                technical_stack=self._extract_technical_stack(content),
                current_team=self._extract_current_team(content),
                open_positions=self._extract_open_positions(content),
                required_skills=self._extract_required_skills(content),
                experience_level=self._extract_field(content, r"Experience Level:\s*(.+)") or "",
                timeline=self._extract_field(content, r"Timeline:\s*(.+)") or "",
                location_preference=self._extract_field(content, r"Location:\s*(.+)") or "",
                raw_content=content
            )
            
            return project
            
        except Exception as e:
            logger.error(f"Error parsing project requirement {file_path}: {e}")
            return None

    # Helper methods for parsing content
    def _extract_field(self, content: str, pattern: str) -> Optional[str]:
        """Extract a single field using regex pattern."""
        match = re.search(pattern, content, re.IGNORECASE | re.MULTILINE)
        return match.group(1).strip() if match else None

    def _extract_boolean_field(self, content: str, pattern: str) -> bool:
        """Extract a boolean field using regex pattern."""
        value = self._extract_field(content, pattern)
        if value:
            return value.lower() in ['yes', 'true', 'preferred', 'open']
        return False

    def _extract_list_field(self, content: str, pattern: str) -> List[str]:
        """Extract a list field using regex pattern."""
        value = self._extract_field(content, pattern)
        if value:
            return [item.strip() for item in value.split(',') if item.strip()]
        return []

    def _extract_date_field(self, content: str, pattern: str) -> datetime:
        """Extract a date field using regex pattern."""
        value = self._extract_field(content, pattern)
        if value:
            try:
                return datetime.strptime(value, "%Y-%m-%d")
            except:
                return datetime.now()
        return datetime.now()

    def _extract_years_experience(self, content: str) -> int:
        """Extract years of experience from content."""
        patterns = [
            r"(\d+)\+?\s*years?\s*(?:of\s*)?experience",
            r"Experience:\s*(\d+)\+?\s*years?",
            r"(\d+)\+?\s*years?\s*in"
        ]

        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return int(match.group(1))
        return 0

    def _parse_skills_from_content(self, content: str) -> List[SkillProficiency]:
        """Parse skills with proficiency levels from content."""
        skills = []

        # Look for skill proficiency assessment section
        proficiency_section = re.search(
            r"SKILL PROFICIENCY ASSESSMENT\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if proficiency_section:
            lines = proficiency_section.group(1).split('\n')
            for line in lines:
                if ':' in line and '%' in line:
                    parts = line.split(':')
                    if len(parts) == 2:
                        skill_name = parts[0].strip()
                        proficiency_str = parts[1].strip().replace('%', '')
                        try:
                            proficiency = float(proficiency_str)
                            level = self._get_skill_level(proficiency)
                            skills.append(SkillProficiency(
                                skill=skill_name,
                                proficiency=proficiency,
                                years_experience=self._estimate_years_from_proficiency(proficiency),
                                level=level
                            ))
                        except ValueError:
                            continue

        # Fallback: extract skills from technical skills section
        if not skills:
            skills_section = re.search(
                r"TECHNICAL SKILLS\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
                content, re.DOTALL | re.IGNORECASE
            )

            if skills_section:
                lines = skills_section.group(1).split('\n')
                for line in lines:
                    if '(' in line and 'years' in line:
                        # Extract skill and years
                        skill_match = re.search(r"•\s*([^(]+)\s*\([^)]*(\d+)\s*years?", line)
                        if skill_match:
                            skill_name = skill_match.group(1).strip()
                            years = int(skill_match.group(2))
                            proficiency = min(95, years * 15 + 20)  # Estimate proficiency
                            level = self._get_skill_level(proficiency)
                            skills.append(SkillProficiency(
                                skill=skill_name,
                                proficiency=proficiency,
                                years_experience=years,
                                level=level
                            ))

        return skills

    def _get_skill_level(self, proficiency: float) -> str:
        """Get skill level based on proficiency percentage."""
        if proficiency >= 90:
            return "Expert"
        elif proficiency >= 70:
            return "Advanced"
        elif proficiency >= 50:
            return "Intermediate"
        else:
            return "Beginner"

    def _estimate_years_from_proficiency(self, proficiency: float) -> int:
        """Estimate years of experience from proficiency percentage."""
        return max(1, int((proficiency - 20) / 15))

    def _parse_okr_objectives(self, content: str) -> List[OKRObjective]:
        """Parse OKR objectives from content."""
        objectives = []

        okr_section = re.search(
            r"CAREER OBJECTIVES.*?OKR.*?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if okr_section:
            objective_blocks = re.findall(
                r"Objective \d+:\s*([^\n]+)\s*Key Results:\s*(.*?)(?=Objective \d+:|$)",
                okr_section.group(1), re.DOTALL
            )

            for obj_title, key_results_text in objective_blocks:
                key_results = []
                progress = 0.0

                # Parse key results
                kr_lines = re.findall(r"•\s*([^(]+)\(Progress:\s*([^)]+)\)", key_results_text)
                for kr_text, progress_text in kr_lines:
                    try:
                        if '/' in progress_text:
                            current, total = progress_text.split('/')
                            kr_progress = (float(current) / float(total)) * 100
                        else:
                            kr_progress = float(progress_text.replace('%', ''))

                        key_results.append({
                            "description": kr_text.strip(),
                            "progress": kr_progress,
                            "status": "In Progress" if kr_progress < 100 else "Completed"
                        })
                        progress += kr_progress
                    except:
                        key_results.append({
                            "description": kr_text.strip(),
                            "progress": 0,
                            "status": "Not Started"
                        })

                if key_results:
                    progress = progress / len(key_results)

                objectives.append(OKRObjective(
                    objective=obj_title.strip(),
                    key_results=key_results,
                    progress=progress,
                    target_date="Q4 2024",  # Default
                    status="In Progress" if progress < 100 else "Completed"
                ))

        return objectives

    def _extract_certifications(self, content: str) -> List[str]:
        """Extract certifications from content."""
        cert_section = re.search(
            r"CERTIFICATIONS?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if cert_section:
            lines = cert_section.group(1).split('\n')
            certs = []
            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    cert = line.strip().replace('•', '').strip()
                    if cert:
                        certs.append(cert)
            return certs
        return []

    def _extract_achievements(self, content: str) -> List[str]:
        """Extract achievements from content."""
        achievement_section = re.search(
            r"ACHIEVEMENTS?\s*(?:&\s*RECOGNITION)?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if achievement_section:
            lines = achievement_section.group(1).split('\n')
            achievements = []
            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    achievement = line.strip().replace('•', '').strip()
                    if achievement:
                        achievements.append(achievement)
            return achievements
        return []

    def _extract_career_goals(self, content: str) -> List[str]:
        """Extract career goals from content."""
        goals_section = re.search(
            r"CAREER\s*(?:DEVELOPMENT\s*)?GOALS?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if goals_section:
            lines = goals_section.group(1).split('\n')
            goals = []
            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    goal = line.strip().replace('•', '').strip()
                    if goal:
                        goals.append(goal)
            return goals
        return []

    def _extract_project_interests(self, content: str) -> List[str]:
        """Extract project interests from content."""
        interests_section = re.search(
            r"PROJECT\s*INTERESTS?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if interests_section:
            lines = interests_section.group(1).split('\n')
            interests = []
            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    interest = line.strip().replace('•', '').strip()
                    if interest:
                        interests.append(interest)
            return interests
        return []

    # Candidate-specific helper methods
    def _generate_skill_radar_data(self, skills: List[SkillProficiency], content: str) -> Dict[str, float]:
        """Generate skill radar chart data for candidates."""
        radar_categories = {
            "Programming": ["Python", "JavaScript", "Java", "C++", "Go", "TypeScript"],
            "Frontend": ["React", "Vue.js", "Angular", "HTML", "CSS", "UI/UX"],
            "Backend": ["Node.js", "Django", "Flask", "Express", "Spring", "API"],
            "Database": ["PostgreSQL", "MongoDB", "MySQL", "Redis", "SQL"],
            "Cloud": ["AWS", "Azure", "GCP", "Docker", "Kubernetes"],
            "Data Science": ["Machine Learning", "TensorFlow", "PyTorch", "Statistics", "Analytics"],
            "DevOps": ["CI/CD", "Jenkins", "GitHub Actions", "Terraform", "Monitoring"],
            "Mobile": ["React Native", "Flutter", "iOS", "Android", "Mobile"]
        }

        radar_data = {}

        for category, keywords in radar_categories.items():
            category_score = 0
            matching_skills = 0

            for skill in skills:
                for keyword in keywords:
                    if keyword.lower() in skill.skill.lower():
                        category_score += skill.proficiency
                        matching_skills += 1
                        break

            if matching_skills > 0:
                radar_data[category] = category_score / matching_skills
            else:
                radar_data[category] = 0

        return radar_data

    def _extract_application_date(self, content: str) -> datetime:
        """Extract application date from content."""
        date_str = self._extract_field(content, r"Application Date:\s*(.+)")
        if date_str:
            try:
                return datetime.strptime(date_str, "%Y-%m-%d")
            except:
                return datetime.now()
        return datetime.now()

    def _extract_interested_projects(self, content: str) -> List[str]:
        """Extract interested projects from content."""
        projects_str = self._extract_field(content, r"Interested Projects:\s*(.+)")
        if projects_str:
            return [p.strip() for p in projects_str.split(',') if p.strip()]
        return []

    def _extract_education(self, content: str) -> str:
        """Extract education information from content."""
        education_section = re.search(
            r"EDUCATION\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if education_section:
            return education_section.group(1).strip()
        return ""

    def _extract_projects(self, content: str) -> List[Dict[str, Any]]:
        """Extract project information from content."""
        projects = []

        projects_section = re.search(
            r"PROJECTS?\s*(?:&\s*ACHIEVEMENTS?)?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if projects_section:
            # Simple extraction - can be enhanced
            lines = projects_section.group(1).split('\n')
            current_project = None

            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    if current_project:
                        projects.append(current_project)
                    current_project = {
                        "name": line.strip().replace('•', '').strip(),
                        "description": "",
                        "technologies": []
                    }
                elif current_project and line.strip():
                    current_project["description"] += line.strip() + " "

            if current_project:
                projects.append(current_project)

        return projects

    # Project-specific helper methods
    def _extract_description(self, content: str) -> str:
        """Extract project description from content."""
        desc_section = re.search(
            r"PROJECT OVERVIEW\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if desc_section:
            return desc_section.group(1).strip()
        return ""

    def _extract_business_objectives(self, content: str) -> List[str]:
        """Extract business objectives from content."""
        obj_section = re.search(
            r"BUSINESS OBJECTIVES?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if obj_section:
            lines = obj_section.group(1).split('\n')
            objectives = []
            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    obj = line.strip().replace('•', '').strip()
                    if obj:
                        objectives.append(obj)
            return objectives
        return []

    def _extract_technical_requirements(self, content: str) -> List[str]:
        """Extract technical requirements from content."""
        req_section = re.search(
            r"TECHNICAL REQUIREMENTS?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if req_section:
            lines = req_section.group(1).split('\n')
            requirements = []
            for line in lines:
                if line.strip() and line.strip().startswith('•'):
                    req = line.strip().replace('•', '').strip()
                    if req:
                        requirements.append(req)
            return requirements
        return []

    def _extract_technical_stack(self, content: str) -> List[str]:
        """Extract technical stack from content."""
        stack_section = re.search(
            r"TECHNICAL STACK\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        if stack_section:
            # Extract technologies mentioned in the stack section
            stack_text = stack_section.group(1)
            technologies = []

            # Common technology patterns
            tech_patterns = [
                r"Python", r"JavaScript", r"React", r"Node\.js", r"AWS", r"Docker",
                r"Kubernetes", r"PostgreSQL", r"MongoDB", r"Redis", r"TensorFlow",
                r"PyTorch", r"Spark", r"Kafka", r"Jenkins", r"GitHub Actions"
            ]

            for pattern in tech_patterns:
                if re.search(pattern, stack_text, re.IGNORECASE):
                    technologies.append(pattern.replace(r"\.js", ".js").replace(r"\.", "."))

            return technologies
        return []

    def _extract_current_team(self, content: str) -> List[Dict[str, Any]]:
        """Extract current team composition from content."""
        team_section = re.search(
            r"CURRENT TEAM COMPOSITION\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        team_members = []
        if team_section:
            lines = team_section.group(1).split('\n')
            current_member = None

            for line in lines:
                if ':' in line and not line.strip().startswith('•'):
                    if current_member:
                        team_members.append(current_member)

                    parts = line.split(':')
                    role = parts[0].strip()
                    name_info = parts[1].strip() if len(parts) > 1 else ""

                    current_member = {
                        "role": role,
                        "name": name_info,
                        "details": []
                    }
                elif current_member and line.strip().startswith('•'):
                    detail = line.strip().replace('•', '').strip()
                    if detail:
                        current_member["details"].append(detail)

            if current_member:
                team_members.append(current_member)

        return team_members

    def _extract_open_positions(self, content: str) -> List[Dict[str, Any]]:
        """Extract open positions from content."""
        positions_section = re.search(
            r"OPEN POSITIONS?\s*[-=]+\s*(.*?)(?=\n[A-Z\s]+[-=]+|\Z)",
            content, re.DOTALL | re.IGNORECASE
        )

        positions = []
        if positions_section:
            # Extract position blocks
            position_blocks = re.findall(
                r"Position \d+:\s*([^\n]+)\s*Role:\s*([^\n]+)\s*Requirements:\s*(.*?)(?=Position \d+:|Salary Range:|$)",
                positions_section.group(1), re.DOTALL
            )

            for title, role, requirements_text in position_blocks:
                requirements = []
                salary = ""
                location = ""

                # Extract requirements
                req_lines = requirements_text.split('\n')
                for line in req_lines:
                    if line.strip().startswith('•'):
                        req = line.strip().replace('•', '').strip()
                        if req:
                            requirements.append(req)
                    elif 'Salary Range:' in line:
                        salary = line.split('Salary Range:')[1].strip()
                    elif 'Location:' in line:
                        location = line.split('Location:')[1].strip()

                positions.append({
                    "title": title.strip(),
                    "role": role.strip(),
                    "requirements": requirements,
                    "salary_range": salary,
                    "location": location
                })

        return positions

    def _extract_required_skills(self, content: str) -> List[str]:
        """Extract required skills from content."""
        skills = []

        # Look in open positions for required skills
        positions = self._extract_open_positions(content)
        for position in positions:
            for req in position.get("requirements", []):
                # Extract technology names from requirements
                tech_keywords = [
                    "Python", "JavaScript", "React", "Node.js", "AWS", "Docker",
                    "Kubernetes", "PostgreSQL", "MongoDB", "TensorFlow", "PyTorch",
                    "Machine Learning", "Data Science", "DevOps", "Full-Stack"
                ]

                for keyword in tech_keywords:
                    if keyword.lower() in req.lower() and keyword not in skills:
                        skills.append(keyword)

        return skills

    # Matching and scoring algorithms
    def calculate_candidate_project_match(self, candidate: CandidateProfile, project: ProjectRequirement) -> float:
        """
        Calculate matching score between candidate and project.

        Returns:
            float: Match score from 0-100
        """
        total_score = 0
        weight_sum = 0

        # Skills matching (40% weight)
        skills_score = self._calculate_skills_match(candidate.skills, project.required_skills)
        total_score += skills_score * 0.4
        weight_sum += 0.4

        # Experience level matching (25% weight)
        experience_score = self._calculate_experience_match(candidate.experience_years, project.experience_level)
        total_score += experience_score * 0.25
        weight_sum += 0.25

        # Location matching (15% weight)
        location_score = self._calculate_location_match(candidate.location, project.location_preference)
        total_score += location_score * 0.15
        weight_sum += 0.15

        # Salary matching (10% weight)
        salary_score = self._calculate_salary_match(candidate.salary_expectation, project.budget)
        total_score += salary_score * 0.10
        weight_sum += 0.10

        # Interest matching (10% weight)
        interest_score = self._calculate_interest_match(candidate.interested_projects, project.name)
        total_score += interest_score * 0.10
        weight_sum += 0.10

        return total_score / weight_sum if weight_sum > 0 else 0

    def _calculate_skills_match(self, candidate_skills: List[SkillProficiency], required_skills: List[str]) -> float:
        """Calculate skills matching score."""
        if not required_skills:
            return 100

        matched_skills = 0
        total_proficiency = 0

        for required_skill in required_skills:
            best_match = 0
            for candidate_skill in candidate_skills:
                if required_skill.lower() in candidate_skill.skill.lower():
                    best_match = max(best_match, candidate_skill.proficiency)

            if best_match > 0:
                matched_skills += 1
                total_proficiency += best_match

        if matched_skills == 0:
            return 0

        # Calculate score based on coverage and proficiency
        coverage_score = (matched_skills / len(required_skills)) * 100
        proficiency_score = total_proficiency / matched_skills

        return (coverage_score + proficiency_score) / 2

    def _calculate_experience_match(self, candidate_years: int, required_level: str) -> float:
        """Calculate experience level matching score."""
        level_requirements = {
            "junior": (0, 2),
            "mid-level": (2, 5),
            "senior": (5, 10),
            "staff": (8, 15),
            "principal": (10, 20)
        }

        required_level_lower = required_level.lower()
        for level, (min_years, max_years) in level_requirements.items():
            if level in required_level_lower:
                if min_years <= candidate_years <= max_years:
                    return 100
                elif candidate_years < min_years:
                    return max(0, 100 - (min_years - candidate_years) * 20)
                else:
                    return max(50, 100 - (candidate_years - max_years) * 10)

        return 75  # Default if level not recognized

    def _calculate_location_match(self, candidate_location: str, project_location: str) -> float:
        """Calculate location matching score."""
        if not project_location or "remote" in project_location.lower():
            return 100

        if candidate_location.lower() in project_location.lower():
            return 100

        # Check for same city/state
        candidate_parts = candidate_location.split(',')
        project_parts = project_location.split(',')

        for c_part in candidate_parts:
            for p_part in project_parts:
                if c_part.strip().lower() == p_part.strip().lower():
                    return 80

        return 30  # Different location but might be willing to relocate

    def _calculate_salary_match(self, candidate_expectation: str, project_budget: str) -> float:
        """Calculate salary matching score."""
        if not candidate_expectation or not project_budget:
            return 75  # Neutral if information not available

        try:
            # Extract salary ranges
            candidate_range = self._extract_salary_range(candidate_expectation)
            project_range = self._extract_salary_range(project_budget)

            if not candidate_range or not project_range:
                return 75

            # Check overlap
            candidate_min, candidate_max = candidate_range
            project_min, project_max = project_range

            # Calculate overlap percentage
            overlap_start = max(candidate_min, project_min)
            overlap_end = min(candidate_max, project_max)

            if overlap_start <= overlap_end:
                overlap = overlap_end - overlap_start
                candidate_range_size = candidate_max - candidate_min
                project_range_size = project_max - project_min

                overlap_percentage = overlap / min(candidate_range_size, project_range_size)
                return min(100, overlap_percentage * 100)
            else:
                # No overlap - calculate distance
                if candidate_min > project_max:
                    # Candidate expects more than project offers
                    gap = candidate_min - project_max
                    return max(0, 100 - (gap / project_max) * 100)
                else:
                    # Project offers more than candidate expects
                    return 100

        except:
            return 75

    def _extract_salary_range(self, salary_str: str) -> Optional[Tuple[float, float]]:
        """Extract salary range from string."""
        # Remove currency symbols and common words
        clean_str = re.sub(r'[$,K]', '', salary_str.upper())

        # Look for range patterns like "100-150" or "100K-150K"
        range_match = re.search(r'(\d+)\s*[-–]\s*(\d+)', clean_str)
        if range_match:
            min_val = float(range_match.group(1)) * 1000  # Assume K
            max_val = float(range_match.group(2)) * 1000
            return (min_val, max_val)

        # Look for single value
        single_match = re.search(r'(\d+)', clean_str)
        if single_match:
            val = float(single_match.group(1)) * 1000
            return (val * 0.9, val * 1.1)  # ±10% range

        return None

    def _calculate_interest_match(self, interested_projects: List[str], project_name: str) -> float:
        """Calculate interest matching score."""
        if not interested_projects:
            return 50  # Neutral if no specific interests

        for interest in interested_projects:
            if interest.lower() in project_name.lower() or project_name.lower() in interest.lower():
                return 100

        # Check for keyword matches
        project_keywords = project_name.lower().split()
        for interest in interested_projects:
            interest_keywords = interest.lower().split()
            common_keywords = set(project_keywords) & set(interest_keywords)
            if len(common_keywords) >= 2:
                return 80

        return 30  # Low interest but not zero

    def update_candidate_project_matches(self):
        """Update project matching scores for all candidates."""
        for candidate in self.candidate_profiles.values():
            candidate.project_matches = {}
            for project in self.project_requirements.values():
                score = self.calculate_candidate_project_match(candidate, project)
                candidate.project_matches[project.id] = score

    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        return {
            "ai_system": "Google Gemini",
            "talent_profiles": len(self.talent_profiles),
            "candidate_profiles": len(self.candidate_profiles),
            "project_requirements": len(self.project_requirements),
            "working_directory": str(self.working_dir),
            "data_directories": {
                "talent": str(self.talent_dir),
                "resume": str(self.resume_dir),
                "projects": str(self.projects_dir)
            },
            "cache_exists": self.working_dir.exists(),
            "timestamp": datetime.now().isoformat()
        }
