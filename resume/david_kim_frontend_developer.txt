CANDIDATE RESUME
================

PERSONAL INFORMATION
--------------------
Name: <PERSON>: <EMAIL>
Phone: +****************
Location: Los Angeles, CA
LinkedIn: linkedin.com/in/davidkim-frontend
GitHub: github.com/davidkim-dev
Portfolio: davidkim-portfolio.dev

APPLICATION STATUS
------------------
Application Date: 2024-07-22
Status: New Application
Interested Projects: Modern SaaS Web Application, E-commerce Platform Frontend
Preferred Role: Senior Frontend Developer
Salary Expectation: $110K - $150K
Remote Work: Preferred
Relocation: Open to Austin, Seattle

PROFESSIONAL EXPERIENCE
------------------------
Airbnb (2022-2024) - Frontend Developer
• Developed booking interface used by 50M+ users with 99.9% uptime
• Implemented real-time messaging system reducing customer support by 30%
• Built responsive design components supporting 15+ languages
• Optimized application performance achieving 95+ Lighthouse scores
• Mentored 3 junior developers and led code review processes

Shopify (2020-2022) - Junior Frontend Developer
• Built e-commerce platform features handling $1M+ daily transactions
• Developed reusable component library used across 20+ applications
• Improved website performance by 60% through optimization techniques
• Collaborated with design team to implement pixel-perfect UI/UX
• Contributed to accessibility improvements achieving WCAG 2.1 AA compliance

Freelance (2019-2020) - Web Developer
• Delivered 15+ client projects ranging from small business websites to web applications
• Specialized in React and modern JavaScript frameworks
• Managed full project lifecycle from requirements to deployment
• Built custom CMS solutions and e-commerce platforms
• Maintained 98% client satisfaction rate

EDUCATION
---------
B.S. Computer Science - UCLA (2019)
Cum Laude, Dean's List (6 semesters)
Senior Project: "Progressive Web Application for Local Business Discovery"
Relevant Coursework: Web Development, Human-Computer Interaction, Software Engineering

TECHNICAL SKILLS
----------------
Frontend Technologies:
• React (Expert) - 5 years
• Vue.js (Advanced) - 3 years
• TypeScript (Expert) - 4 years
• JavaScript (Expert) - 6 years
• HTML5/CSS3 (Expert) - 6 years
• Next.js (Advanced) - 3 years
• Svelte (Intermediate) - 1 year

Styling & Design:
• Tailwind CSS (Expert) - 3 years
• Styled Components (Advanced) - 3 years
• SASS/SCSS (Advanced) - 4 years
• CSS-in-JS (Advanced) - 3 years
• Responsive Design (Expert) - 5 years
• Figma (Advanced) - 3 years

State Management:
• Redux (Advanced) - 4 years
• Zustand (Advanced) - 2 years
• Context API (Expert) - 4 years
• MobX (Intermediate) - 2 years

Testing & Quality:
• Jest (Advanced) - 4 years
• React Testing Library (Advanced) - 3 years
• Cypress (Advanced) - 3 years
• Playwright (Intermediate) - 1 year
• ESLint/Prettier (Expert) - 4 years

Build Tools & DevOps:
• Webpack (Advanced) - 4 years
• Vite (Advanced) - 2 years
• Docker (Intermediate) - 2 years
• GitHub Actions (Advanced) - 2 years
• Vercel/Netlify (Expert) - 3 years

Backend (Basic):
• Node.js (Intermediate) - 2 years
• Express.js (Intermediate) - 2 years
• GraphQL (Intermediate) - 2 years
• REST APIs (Advanced) - 4 years

SKILL PROFICIENCY ASSESSMENT
-----------------------------
React Development: 95%
TypeScript: 90%
CSS/Styling: 95%
JavaScript: 95%
UI/UX Design: 80%
Performance Optimization: 85%
Testing: 80%
Responsive Design: 95%
Accessibility: 75%
Project Management: 70%

PROJECTS & ACHIEVEMENTS
-----------------------
• Built open-source React component library with 15K+ GitHub stars
• Airbnb Hackathon Winner 2023 - "Accessible Travel Planning Tool"
• Speaker at React Conference 2023 - "Performance Optimization Techniques"
• Contributor to major open-source projects (React, Next.js, Tailwind CSS)
• Technical blog with 25K+ monthly readers
• Mentor in coding bootcamp programs

CERTIFICATIONS
--------------
• Google UX Design Certificate (2023)
• AWS Certified Cloud Practitioner (2022)
• Meta Frontend Developer Certificate (2021)
• Accessibility Specialist Certification (2021)

PORTFOLIO HIGHLIGHTS
--------------------
1. E-commerce Platform (React, TypeScript, Stripe)
   - Full-featured online store with payment processing
   - 99.9% uptime, mobile-first design
   - Used by 10K+ customers

2. Real-time Chat Application (React, Socket.io, Node.js)
   - Instant messaging with file sharing
   - End-to-end encryption, group chats
   - 1K+ concurrent users supported

3. Data Visualization Dashboard (React, D3.js, Chart.js)
   - Interactive analytics platform
   - Real-time data updates, custom charts
   - Used by Fortune 500 company

PROJECT MATCHING PREFERENCES
-----------------------------
Preferred Project Types:
• Modern web applications with complex UI
• E-commerce and fintech platforms
• Real-time applications and dashboards
• Progressive web applications (PWAs)
• Design system and component library development

Technology Preferences:
• React ecosystem (Next.js, Gatsby)
• TypeScript for type safety
• Modern CSS frameworks (Tailwind, Styled Components)
• GraphQL for data fetching
• Serverless deployment (Vercel, Netlify)

Team Size Preference: 3-10 people
Project Duration: 3-12 months
Leadership Role: Open to tech lead opportunities

CAREER GOALS
------------
• Become a Senior/Staff Frontend Engineer at a major tech company
• Specialize in performance optimization and accessibility
• Lead frontend architecture decisions for large-scale applications
• Contribute to open-source frontend frameworks and tools
• Mentor junior developers and build inclusive engineering teams

WORK PREFERENCES
----------------
• Remote-first or hybrid work environment
• Collaborative teams with strong design partnership
• Opportunities for technical growth and learning
• Access to latest frontend technologies and tools
• Flexible work hours and work-life balance

AVAILABILITY
------------
Start Date: Available in 2 weeks
Notice Period: 2 weeks (current position)
Contract Type: Full-time preferred, open to contract
Travel: Up to 10%
Visa Status: US Citizen

REFERENCES
----------
Sarah Chen - Senior Engineering Manager at Airbnb - <EMAIL>
Mike Rodriguez - Lead Developer at Shopify - <EMAIL>
Jennifer Liu - UX Designer at Airbnb - <EMAIL>

ADDITIONAL INFORMATION
----------------------
• Passionate about web accessibility and inclusive design
• Active contributor to frontend development community
• Regular attendee of tech conferences and meetups
• Experienced in agile development methodologies
• Strong communication skills and cross-functional collaboration

LAST UPDATED: 2024-07-22
