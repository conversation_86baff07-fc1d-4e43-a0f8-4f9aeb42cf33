CANDIDATE RESUME
================

PERSONAL INFORMATION
--------------------
Name: <PERSON>ail: <EMAIL>
Phone: +****************
Location: New York, NY
LinkedIn: linkedin.com/in/sarah<PERSON><PERSON>son-ds
GitHub: github.com/sarah<PERSON>hnson-data
Portfolio: sarah<PERSON><PERSON>son-portfolio.com

APPLICATION STATUS
------------------
Application Date: 2024-07-20
Status: Under Review
Interested Projects: AI-Powered E-commerce Recommendation Engine, Data Science Platform
Preferred Role: Senior Data Scientist
Salary Expectation: $140K - $180K
Remote Work: Yes
Relocation: Open to San Francisco, Seattle, Austin

PROFESSIONAL EXPERIENCE
------------------------
Netflix (2021-2024) - Senior Data Scientist
• Built recommendation system serving 200M+ users globally with 25% improvement in engagement
• Developed A/B testing framework reducing experiment time by 50%
• Led cross-functional team of 6 data scientists and engineers
• Implemented real-time ML pipelines processing 1TB+ data daily
• Published internal research on deep learning for content recommendation

Uber (2019-2021) - ML Engineer
• Developed real-time pricing algorithms for ride-sharing platform
• Built MLOps pipeline processing 1B+ events daily with 99.9% reliability
• Reduced model inference latency by 70% through optimization
• Collaborated with product teams to implement surge pricing models
• Mentored 3 junior data scientists and established best practices

Facebook (2018-2019) - Data Analyst
• Analyzed user engagement patterns for 2B+ users across multiple platforms
• Built dashboards and reports used by 100+ product managers
• Developed statistical models for user retention and growth prediction
• Conducted large-scale experiments to optimize user experience
• Contributed to data infrastructure improvements

EDUCATION
---------
Ph.D. Computer Science (AI/ML) - MIT (2018)
Dissertation: "Deep Learning for Personalized Recommendation Systems"
Advisor: Dr. Regina Barzilay
GPA: 3.9/4.0

M.S. Statistics - Stanford University (2015)
Thesis: "Bayesian Methods for Large-Scale Data Analysis"
GPA: 3.8/4.0

B.S. Mathematics - Harvard University (2014)
Magna Cum Laude, Phi Beta Kappa
Concentration: Applied Mathematics and Statistics

TECHNICAL SKILLS
----------------
Programming Languages:
• Python (Expert) - 6 years
• R (Advanced) - 5 years
• SQL (Expert) - 6 years
• Scala (Intermediate) - 3 years
• Java (Intermediate) - 2 years

Machine Learning & AI:
• TensorFlow (Expert) - 5 years
• PyTorch (Advanced) - 4 years
• Scikit-learn (Expert) - 6 years
• XGBoost (Advanced) - 4 years
• Keras (Advanced) - 4 years
• MLflow (Advanced) - 3 years

Big Data Technologies:
• Spark (Expert) - 4 years
• Hadoop (Advanced) - 3 years
• Kafka (Advanced) - 3 years
• Airflow (Advanced) - 3 years
• Databricks (Advanced) - 2 years

Cloud Platforms:
• AWS (Advanced) - 4 years
• Azure ML (Intermediate) - 2 years
• Google Cloud Platform (Advanced) - 3 years
• Snowflake (Intermediate) - 2 years

Visualization & Analytics:
• Tableau (Expert) - 5 years
• Power BI (Advanced) - 3 years
• D3.js (Intermediate) - 2 years
• Plotly (Advanced) - 3 years
• Jupyter (Expert) - 6 years

SKILL PROFICIENCY ASSESSMENT
-----------------------------
Machine Learning: 95%
Statistics: 90%
Python Programming: 95%
Data Engineering: 80%
Cloud Computing: 75%
Business Intelligence: 85%
Deep Learning: 90%
Big Data Processing: 85%
Data Visualization: 80%
Project Management: 70%

PROJECTS & ACHIEVEMENTS
-----------------------
• 15+ publications in top-tier ML conferences (NeurIPS, ICML, KDD)
• Kaggle Grandmaster with 3 gold medals
• Netflix Innovation Award 2023 for recommendation system improvements
• Open-source contributor to TensorFlow and Scikit-learn
• Speaker at 8 data science conferences
• Mentor in Women in Data Science program

CERTIFICATIONS
--------------
• AWS Certified Machine Learning - Specialty (2023)
• Google Cloud Professional ML Engineer (2022)
• Databricks Certified Data Scientist (2021)
• Tableau Desktop Certified Professional (2020)

RESEARCH INTERESTS
------------------
• Recommendation systems and personalization
• Deep learning for sequential data
• Causal inference and experimentation
• Ethical AI and fairness in ML
• Large-scale distributed machine learning

PROJECT MATCHING PREFERENCES
-----------------------------
Preferred Project Types:
• AI/ML research and development
• Recommendation systems
• Data platform engineering
• Experimentation and A/B testing
• Real-time ML systems

Industry Preferences:
• Technology and streaming
• E-commerce and retail
• Financial services
• Healthcare and biotech
• Social media and networking

Team Size Preference: 5-15 people
Project Duration: 6+ months
Leadership Role: Yes, interested in leading data science teams

CAREER GOALS
------------
• Lead data science initiatives at a major tech company
• Publish cutting-edge research in ML and AI
• Build and scale data science teams
• Develop products that impact millions of users
• Contribute to ethical AI and responsible ML practices

AVAILABILITY
------------
Start Date: Available immediately
Notice Period: 2 weeks (if employed)
Contract Type: Full-time preferred, open to contract
Travel: Up to 25%
Visa Status: US Citizen

REFERENCES
----------
Dr. Regina Barzilay - MIT Professor - <EMAIL>
John Smith - Former Manager at Netflix - <EMAIL>
Lisa Wang - Director of Data Science at Uber - <EMAIL>

ADDITIONAL INFORMATION
----------------------
• Fluent in English, Spanish, and Mandarin
• Active in data science community and mentorship programs
• Regular contributor to technical blogs and publications
• Passionate about using data science for social good
• Experienced in remote work and distributed teams

LAST UPDATED: 2024-07-20
