#!/usr/bin/env python3
"""
Comprehensive Streamlit UI for Gemini Talent Matching System v2.0

This application provides a complete user interface with all specified components:
- Projects List and Details Views
- Talent List and Details Views with OKR framework
- Candidates List and Details Views with radar charts and matching scores
- Advanced search and filtering capabilities
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import asyncio
import json
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from gemini_talent_matcher_v2 import GeminiTalentMatcherV2, TalentProfile, CandidateProfile, ProjectRequirement
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="Gemini Talent Matching System v2.0",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.8rem;
        font-weight: bold;
        color: #2e8b57;
        margin-top: 2rem;
        margin-bottom: 1rem;
        border-bottom: 2px solid #2e8b57;
        padding-bottom: 0.5rem;
    }
    .subsection-header {
        font-size: 1.3rem;
        font-weight: bold;
        color: #4682b4;
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
    }
    .info-box {
        background-color: #f0f8ff;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #f0fff0;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #32cd32;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff8dc;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ffa500;
        margin: 1rem 0;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }
    .skill-badge {
        display: inline-block;
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 0.2rem 0.5rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        margin: 0.1rem;
    }
    .status-active {
        color: #4caf50;
        font-weight: bold;
    }
    .status-planning {
        color: #ff9800;
        font-weight: bold;
    }
    .status-completed {
        color: #9e9e9e;
        font-weight: bold;
    }
    .match-score-high {
        color: #4caf50;
        font-weight: bold;
        font-size: 1.2rem;
    }
    .match-score-medium {
        color: #ff9800;
        font-weight: bold;
        font-size: 1.2rem;
    }
    .match-score-low {
        color: #f44336;
        font-weight: bold;
        font-size: 1.2rem;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
def initialize_session_state():
    """Initialize session state variables."""
    if 'matcher' not in st.session_state:
        st.session_state.matcher = None
    if 'system_initialized' not in st.session_state:
        st.session_state.system_initialized = False
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'dashboard'
    if 'selected_project' not in st.session_state:
        st.session_state.selected_project = None
    if 'selected_talent' not in st.session_state:
        st.session_state.selected_talent = None
    if 'selected_candidate' not in st.session_state:
        st.session_state.selected_candidate = None
    if 'data_loaded' not in st.session_state:
        st.session_state.data_loaded = False

def initialize_system():
    """Initialize the Gemini talent matching system."""
    try:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key or api_key == "your_google_api_key_here":
            st.error("⚠️ Google API key not found. Please set GOOGLE_API_KEY in your .env file.")
            return False
        
        st.session_state.matcher = GeminiTalentMatcherV2(
            working_dir="./streamlit_gemini_v2_cache",
            log_level="ERROR"  # Reduce noise in UI
        )
        
        # Load all data from files
        with st.spinner("Loading data from files..."):
            stats = st.session_state.matcher.load_all_data()
            
            if stats["talents_loaded"] > 0 or stats["candidates_loaded"] > 0 or stats["projects_loaded"] > 0:
                # Update candidate project matches
                st.session_state.matcher.update_candidate_project_matches()
                st.session_state.data_loaded = True
                st.session_state.system_initialized = True
                
                st.success(f"✅ System initialized! Loaded {stats['talents_loaded']} talents, "
                          f"{stats['candidates_loaded']} candidates, {stats['projects_loaded']} projects")
                return True
            else:
                st.warning("⚠️ No data files found. Please ensure data files exist in talent/, resume/, and projects/ directories.")
                return False
        
    except Exception as e:
        st.error(f"Failed to initialize system: {e}")
        return False

def render_sidebar():
    """Render the sidebar navigation."""
    with st.sidebar:
        st.markdown("### 🎯 Navigation")
        
        # System status
        if st.session_state.system_initialized:
            st.markdown('<div class="success-box">✅ System Ready</div>', unsafe_allow_html=True)
        else:
            st.markdown('<div class="warning-box">⚠️ System Not Initialized</div>', unsafe_allow_html=True)
            if st.button("🚀 Initialize System", type="primary"):
                initialize_system()
        
        # Navigation menu
        if st.session_state.system_initialized:
            view_options = {
                "dashboard": "📊 Dashboard",
                "projects_list": "📋 Projects List",
                "talents_list": "👥 Talents List", 
                "candidates_list": "📄 Candidates List"
            }
            
            selected_view = st.radio(
                "Select View",
                options=list(view_options.keys()),
                format_func=lambda x: view_options[x],
                index=list(view_options.keys()).index(st.session_state.current_view)
            )
            
            if selected_view != st.session_state.current_view:
                st.session_state.current_view = selected_view
                st.rerun()
        
        # System statistics
        if st.session_state.system_initialized and st.session_state.matcher:
            st.markdown("### 📊 System Statistics")
            stats = st.session_state.matcher.get_system_stats()
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Talents", stats["talent_profiles"])
                st.metric("Candidates", stats["candidate_profiles"])
            with col2:
                st.metric("Projects", stats["project_requirements"])
                st.metric("AI System", "Gemini")

def render_dashboard():
    """Render the main dashboard view."""
    st.markdown('<div class="main-header">🎯 Gemini Talent Matching System v2.0</div>', unsafe_allow_html=True)
    
    if not st.session_state.system_initialized:
        st.markdown("""
        <div class="info-box">
        <h3>Welcome to the Gemini Talent Matching System v2.0</h3>
        <p>This comprehensive system provides advanced talent matching capabilities with:</p>
        <ul>
            <li><strong>Projects Management</strong> - View and manage project requirements</li>
            <li><strong>Talent Profiles</strong> - Established professionals with OKR tracking</li>
            <li><strong>Candidate Management</strong> - New applicants with skill assessment</li>
            <li><strong>AI-Powered Matching</strong> - Google Gemini for intelligent recommendations</li>
        </ul>
        <p>Click "Initialize System" in the sidebar to get started.</p>
        </div>
        """, unsafe_allow_html=True)
        return
    
    # System overview metrics
    stats = st.session_state.matcher.get_system_stats()
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric(
            label="📋 Active Projects",
            value=stats["project_requirements"],
            help="Total number of projects in the system"
        )
    with col2:
        st.metric(
            label="👥 Talent Profiles",
            value=stats["talent_profiles"],
            help="Established professionals available for projects"
        )
    with col3:
        st.metric(
            label="📄 Candidates",
            value=stats["candidate_profiles"],
            help="New applicants seeking opportunities"
        )
    with col4:
        st.metric(
            label="🤖 AI System",
            value="Gemini",
            help="Google Gemini AI powering the matching system"
        )
    
    # Quick actions
    st.markdown('<div class="section-header">🚀 Quick Actions</div>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📋 View All Projects", use_container_width=True):
            st.session_state.current_view = "projects_list"
            st.rerun()
    
    with col2:
        if st.button("👥 Browse Talents", use_container_width=True):
            st.session_state.current_view = "talents_list"
            st.rerun()
    
    with col3:
        if st.button("📄 Review Candidates", use_container_width=True):
            st.session_state.current_view = "candidates_list"
            st.rerun()
    
    # Recent activity summary
    if st.session_state.data_loaded:
        st.markdown('<div class="section-header">📈 System Overview</div>', unsafe_allow_html=True)
        
        # Project status distribution
        projects = list(st.session_state.matcher.project_requirements.values())
        if projects:
            status_counts = {}
            for project in projects:
                status = project.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("#### Project Status Distribution")
                fig = px.pie(
                    values=list(status_counts.values()),
                    names=list(status_counts.keys()),
                    title="Projects by Status"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                st.markdown("#### Candidate Application Status")
                candidates = list(st.session_state.matcher.candidate_profiles.values())
                if candidates:
                    candidate_status = {}
                    for candidate in candidates:
                        status = candidate.status
                        candidate_status[status] = candidate_status.get(status, 0) + 1
                    
                    fig = px.bar(
                        x=list(candidate_status.keys()),
                        y=list(candidate_status.values()),
                        title="Candidates by Application Status"
                    )
                    st.plotly_chart(fig, use_container_width=True)

def main():
    """Main application function."""
    initialize_session_state()
    render_sidebar()
    
    # Route to appropriate view
    if st.session_state.current_view == "dashboard":
        render_dashboard()
    elif st.session_state.current_view == "projects_list":
        render_projects_list()
    elif st.session_state.current_view == "project_details":
        render_project_details()
    elif st.session_state.current_view == "talents_list":
        render_talents_list()
    elif st.session_state.current_view == "talent_details":
        render_talent_details()
    elif st.session_state.current_view == "candidates_list":
        render_candidates_list()
    elif st.session_state.current_view == "candidate_details":
        render_candidate_details()

# Projects List and Details Views
def render_projects_list():
    """Render the projects list view."""
    st.markdown('<div class="section-header">📋 Projects List</div>', unsafe_allow_html=True)

    if not st.session_state.system_initialized:
        st.warning("Please initialize the system first.")
        return

    projects = list(st.session_state.matcher.project_requirements.values())

    if not projects:
        st.info("No projects found. Please add project files to the projects/ directory.")
        return

    # Search and filter controls
    col1, col2, col3 = st.columns(3)

    with col1:
        search_term = st.text_input("🔍 Search Projects", placeholder="Enter project name or keyword...")

    with col2:
        status_filter = st.selectbox(
            "Filter by Status",
            options=["All"] + list(set(p.status for p in projects))
        )

    with col3:
        priority_filter = st.selectbox(
            "Filter by Priority",
            options=["All"] + list(set(p.priority for p in projects))
        )

    # Filter projects
    filtered_projects = projects

    if search_term:
        filtered_projects = [
            p for p in filtered_projects
            if search_term.lower() in p.name.lower() or search_term.lower() in p.description.lower()
        ]

    if status_filter != "All":
        filtered_projects = [p for p in filtered_projects if p.status == status_filter]

    if priority_filter != "All":
        filtered_projects = [p for p in filtered_projects if p.priority == priority_filter]

    st.write(f"Showing {len(filtered_projects)} of {len(projects)} projects")

    # Projects grid
    for i in range(0, len(filtered_projects), 2):
        col1, col2 = st.columns(2)

        for j, col in enumerate([col1, col2]):
            if i + j < len(filtered_projects):
                project = filtered_projects[i + j]

                with col:
                    with st.container():
                        # Project card
                        st.markdown(f"""
                        <div class="metric-card">
                            <h4>{project.name}</h4>
                            <p><strong>Company:</strong> {project.company}</p>
                            <p><strong>Status:</strong> <span class="status-{project.status.lower()}">{project.status}</span></p>
                            <p><strong>Priority:</strong> {project.priority}</p>
                            <p><strong>Timeline:</strong> {project.start_date.strftime('%Y-%m-%d')} - {project.end_date.strftime('%Y-%m-%d')}</p>
                            <p><strong>Open Positions:</strong> {len(project.open_positions)}</p>
                            <p>{project.description[:150]}...</p>
                        </div>
                        """, unsafe_allow_html=True)

                        if st.button(f"View Details", key=f"project_{project.id}"):
                            st.session_state.selected_project = project.id
                            st.session_state.current_view = "project_details"
                            st.rerun()

def render_project_details():
    """Render the project details view."""
    if not st.session_state.selected_project:
        st.error("No project selected")
        return

    project = st.session_state.matcher.project_requirements.get(st.session_state.selected_project)
    if not project:
        st.error("Project not found")
        return

    # Back button
    if st.button("← Back to Projects List"):
        st.session_state.current_view = "projects_list"
        st.session_state.selected_project = None
        st.rerun()

    # Project header
    st.markdown(f'<div class="main-header">{project.name}</div>', unsafe_allow_html=True)

    # Project overview
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Status", project.status)
    with col2:
        st.metric("Priority", project.priority)
    with col3:
        st.metric("Budget", project.budget)
    with col4:
        st.metric("Open Positions", len(project.open_positions))

    # Project information tabs
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📋 Overview", "👥 Team", "💼 Open Positions", "🎯 Requirements", "📊 Candidates"])

    with tab1:
        st.markdown('<div class="subsection-header">Project Overview</div>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Company:** " + project.company)
            st.markdown("**Department:** " + project.department)
            st.markdown("**Project Manager:** " + project.project_manager)
            st.markdown("**Timeline:** " + f"{project.start_date.strftime('%Y-%m-%d')} - {project.end_date.strftime('%Y-%m-%d')}")

        with col2:
            st.markdown("**Status:** " + project.status)
            st.markdown("**Priority:** " + project.priority)
            st.markdown("**Budget:** " + project.budget)

        st.markdown('<div class="subsection-header">Description</div>', unsafe_allow_html=True)
        st.write(project.description)

        if project.business_objectives:
            st.markdown('<div class="subsection-header">Business Objectives</div>', unsafe_allow_html=True)
            for obj in project.business_objectives:
                st.write(f"• {obj}")

        if project.technical_stack:
            st.markdown('<div class="subsection-header">Technical Stack</div>', unsafe_allow_html=True)
            tech_badges = ""
            for tech in project.technical_stack:
                tech_badges += f'<span class="skill-badge">{tech}</span> '
            st.markdown(tech_badges, unsafe_allow_html=True)

    with tab2:
        st.markdown('<div class="subsection-header">Current Team</div>', unsafe_allow_html=True)

        if project.current_team:
            for member in project.current_team:
                with st.expander(f"{member['role']} - {member['name']}"):
                    for detail in member.get('details', []):
                        st.write(f"• {detail}")
        else:
            st.info("No current team information available.")

    with tab3:
        st.markdown('<div class="subsection-header">Open Positions</div>', unsafe_allow_html=True)

        if project.open_positions:
            for position in project.open_positions:
                with st.expander(f"{position['title']} - {position['role']}"):
                    st.markdown("**Requirements:**")
                    for req in position.get('requirements', []):
                        st.write(f"• {req}")

                    if position.get('salary_range'):
                        st.markdown(f"**Salary Range:** {position['salary_range']}")

                    if position.get('location'):
                        st.markdown(f"**Location:** {position['location']}")
        else:
            st.info("No open positions available.")

    with tab4:
        st.markdown('<div class="subsection-header">Technical Requirements</div>', unsafe_allow_html=True)

        if project.technical_requirements:
            for req in project.technical_requirements:
                st.write(f"• {req}")

        if project.required_skills:
            st.markdown('<div class="subsection-header">Required Skills</div>', unsafe_allow_html=True)
            skills_badges = ""
            for skill in project.required_skills:
                skills_badges += f'<span class="skill-badge">{skill}</span> '
            st.markdown(skills_badges, unsafe_allow_html=True)

    with tab5:
        st.markdown('<div class="subsection-header">Matching Candidates</div>', unsafe_allow_html=True)

        # Find matching candidates
        candidates = list(st.session_state.matcher.candidate_profiles.values())
        if candidates:
            # Calculate match scores and sort
            candidate_matches = []
            for candidate in candidates:
                score = st.session_state.matcher.calculate_candidate_project_match(candidate, project)
                candidate_matches.append((candidate, score))

            candidate_matches.sort(key=lambda x: x[1], reverse=True)

            # Display top matches
            st.write(f"Top {min(10, len(candidate_matches))} matching candidates:")

            for candidate, score in candidate_matches[:10]:
                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    st.write(f"**{candidate.name}** - {candidate.preferred_role}")
                    st.write(f"📧 {candidate.email} | 📍 {candidate.location}")

                with col2:
                    score_class = "high" if score >= 80 else "medium" if score >= 60 else "low"
                    st.markdown(f'<div class="match-score-{score_class}">{score:.1f}%</div>', unsafe_allow_html=True)

                with col3:
                    if st.button("View Profile", key=f"candidate_{candidate.id}_from_project"):
                        st.session_state.selected_candidate = candidate.id
                        st.session_state.current_view = "candidate_details"
                        st.rerun()
        else:
            st.info("No candidates available for matching.")

# Talents List and Details Views
def render_talents_list():
    """Render the talents list view."""
    st.markdown('<div class="section-header">👥 Talent Profiles</div>', unsafe_allow_html=True)

    if not st.session_state.system_initialized:
        st.warning("Please initialize the system first.")
        return

    talents = list(st.session_state.matcher.talent_profiles.values())

    if not talents:
        st.info("No talent profiles found. Please add talent files to the talent/ directory.")
        return

    # Search and filter controls
    col1, col2, col3 = st.columns(3)

    with col1:
        search_term = st.text_input("🔍 Search Talents", placeholder="Enter name or skill...")

    with col2:
        availability_filter = st.selectbox(
            "Filter by Availability",
            options=["All"] + list(set(t.availability for t in talents if t.availability))
        )

    with col3:
        experience_filter = st.selectbox(
            "Filter by Experience",
            options=["All", "0-2 years", "3-5 years", "6-10 years", "10+ years"]
        )

    # Filter talents
    filtered_talents = talents

    if search_term:
        filtered_talents = [
            t for t in filtered_talents
            if search_term.lower() in t.name.lower() or
               search_term.lower() in t.title.lower() or
               any(search_term.lower() in skill.skill.lower() for skill in t.skills)
        ]

    if availability_filter != "All":
        filtered_talents = [t for t in filtered_talents if t.availability == availability_filter]

    if experience_filter != "All":
        if experience_filter == "0-2 years":
            filtered_talents = [t for t in filtered_talents if t.experience_years <= 2]
        elif experience_filter == "3-5 years":
            filtered_talents = [t for t in filtered_talents if 3 <= t.experience_years <= 5]
        elif experience_filter == "6-10 years":
            filtered_talents = [t for t in filtered_talents if 6 <= t.experience_years <= 10]
        elif experience_filter == "10+ years":
            filtered_talents = [t for t in filtered_talents if t.experience_years > 10]

    st.write(f"Showing {len(filtered_talents)} of {len(talents)} talents")

    # Talents grid
    for i in range(0, len(filtered_talents), 2):
        col1, col2 = st.columns(2)

        for j, col in enumerate([col1, col2]):
            if i + j < len(filtered_talents):
                talent = filtered_talents[i + j]

                with col:
                    with st.container():
                        # Talent card
                        st.markdown(f"""
                        <div class="metric-card">
                            <h4>{talent.name}</h4>
                            <p><strong>Title:</strong> {talent.title}</p>
                            <p><strong>Company:</strong> {talent.current_company}</p>
                            <p><strong>Experience:</strong> {talent.experience_years} years</p>
                            <p><strong>Location:</strong> {talent.location}</p>
                            <p><strong>Availability:</strong> {talent.availability}</p>
                        </div>
                        """, unsafe_allow_html=True)

                        # Top skills
                        if talent.skills:
                            top_skills = sorted(talent.skills, key=lambda x: x.proficiency, reverse=True)[:3]
                            skills_text = " | ".join([f"{s.skill} ({s.level})" for s in top_skills])
                            st.caption(f"🔧 {skills_text}")

                        if st.button(f"View Profile", key=f"talent_{talent.id}"):
                            st.session_state.selected_talent = talent.id
                            st.session_state.current_view = "talent_details"
                            st.rerun()

def render_talent_details():
    """Render the talent details view with OKR framework."""
    if not st.session_state.selected_talent:
        st.error("No talent selected")
        return

    talent = st.session_state.matcher.talent_profiles.get(st.session_state.selected_talent)
    if not talent:
        st.error("Talent not found")
        return

    # Back button
    if st.button("← Back to Talents List"):
        st.session_state.current_view = "talents_list"
        st.session_state.selected_talent = None
        st.rerun()

    # Talent header
    st.markdown(f'<div class="main-header">{talent.name}</div>', unsafe_allow_html=True)
    st.markdown(f"**{talent.title}** at **{talent.current_company}**")

    # Contact and basic info
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Experience", f"{talent.experience_years} years")
    with col2:
        st.metric("Location", talent.location)
    with col3:
        st.metric("Availability", talent.availability)
    with col4:
        if talent.salary_expectation:
            st.metric("Salary Expectation", talent.salary_expectation)

    # Talent information tabs
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["👤 Profile", "🎯 Career Planning (OKR)", "🔧 Skills", "📈 Experience", "💼 Interests"])

    with tab1:
        st.markdown('<div class="subsection-header">Professional Profile</div>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Contact Information:**")
            st.write(f"📧 {talent.email}")
            st.write(f"📞 {talent.phone}")
            st.write(f"🔗 [LinkedIn]({talent.linkedin})" if talent.linkedin else "")
            st.write(f"💻 [GitHub]({talent.github})" if talent.github else "")

            st.markdown("**Current Position:**")
            st.write(f"**Role:** {talent.current_role}")
            st.write(f"**Company:** {talent.current_company}")
            st.write(f"**Status:** {talent.employment_status}")

        with col2:
            st.markdown("**Education:**")
            st.write(talent.education)

            if talent.certifications:
                st.markdown("**Certifications:**")
                for cert in talent.certifications:
                    st.write(f"• {cert}")

            if talent.achievements:
                st.markdown("**Key Achievements:**")
                for achievement in talent.achievements[:5]:  # Show top 5
                    st.write(f"• {achievement}")

        if talent.preferred_work_environment:
            st.markdown('<div class="subsection-header">Work Preferences</div>', unsafe_allow_html=True)
            st.write(talent.preferred_work_environment)

    with tab2:
        st.markdown('<div class="subsection-header">🎯 Career Planning - OKR Framework</div>', unsafe_allow_html=True)

        if talent.okr_objectives:
            for i, okr in enumerate(talent.okr_objectives):
                with st.expander(f"Objective {i+1}: {okr.objective}", expanded=True):
                    # Progress bar for overall objective
                    st.progress(okr.progress / 100, text=f"Overall Progress: {okr.progress:.1f}%")

                    st.markdown("**Key Results:**")
                    for kr in okr.key_results:
                        col1, col2, col3 = st.columns([3, 1, 1])

                        with col1:
                            st.write(f"• {kr['description']}")
                        with col2:
                            st.write(f"{kr['progress']:.1f}%")
                        with col3:
                            status_color = "🟢" if kr['status'] == "Completed" else "🟡" if kr['status'] == "In Progress" else "🔴"
                            st.write(f"{status_color} {kr['status']}")

                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Target Date:** {okr.target_date}")
                    with col2:
                        st.write(f"**Status:** {okr.status}")
        else:
            st.info("No OKR objectives defined for this talent.")

        # Career goals
        if talent.career_goals:
            st.markdown('<div class="subsection-header">Career Development Goals</div>', unsafe_allow_html=True)
            for goal in talent.career_goals:
                st.write(f"• {goal}")

    with tab3:
        st.markdown('<div class="subsection-header">Technical Skills</div>', unsafe_allow_html=True)

        if talent.skills:
            # Skills by category
            skill_categories = {}
            for skill in talent.skills:
                # Simple categorization based on skill name
                category = "Other"
                if any(keyword in skill.skill.lower() for keyword in ["python", "java", "javascript", "c++", "go"]):
                    category = "Programming Languages"
                elif any(keyword in skill.skill.lower() for keyword in ["react", "vue", "angular", "html", "css"]):
                    category = "Frontend"
                elif any(keyword in skill.skill.lower() for keyword in ["node", "django", "flask", "spring"]):
                    category = "Backend"
                elif any(keyword in skill.skill.lower() for keyword in ["aws", "azure", "gcp", "docker", "kubernetes"]):
                    category = "Cloud & DevOps"
                elif any(keyword in skill.skill.lower() for keyword in ["tensorflow", "pytorch", "machine learning", "ai"]):
                    category = "AI/ML"

                if category not in skill_categories:
                    skill_categories[category] = []
                skill_categories[category].append(skill)

            # Display skills by category
            for category, skills in skill_categories.items():
                st.markdown(f"**{category}:**")

                for skill in sorted(skills, key=lambda x: x.proficiency, reverse=True):
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        st.write(skill.skill)
                    with col2:
                        st.progress(skill.proficiency / 100, text=f"{skill.proficiency:.0f}%")
                    with col3:
                        st.write(skill.level)
                    with col4:
                        st.write(f"{skill.years_experience} yrs")

                st.write("")  # Add spacing
        else:
            st.info("No skills information available.")

    with tab4:
        st.markdown('<div class="subsection-header">Professional Experience</div>', unsafe_allow_html=True)

        if talent.previous_companies:
            st.markdown("**Previous Companies:**")
            for company in talent.previous_companies:
                st.write(f"• {company}")

        # Experience timeline could be added here with more detailed parsing
        st.write(f"**Total Experience:** {talent.experience_years} years")

    with tab5:
        st.markdown('<div class="subsection-header">Project Interests</div>', unsafe_allow_html=True)

        if talent.project_interests:
            for interest in talent.project_interests:
                st.write(f"• {interest}")
        else:
            st.info("No specific project interests listed.")

# Candidates List and Details Views
def render_candidates_list():
    """Render the candidates list view."""
    st.markdown('<div class="section-header">📄 Candidate Profiles</div>', unsafe_allow_html=True)

    if not st.session_state.system_initialized:
        st.warning("Please initialize the system first.")
        return

    candidates = list(st.session_state.matcher.candidate_profiles.values())

    if not candidates:
        st.info("No candidate profiles found. Please add resume files to the resume/ directory.")
        return

    # Search and filter controls
    col1, col2, col3 = st.columns(3)

    with col1:
        search_term = st.text_input("🔍 Search Candidates", placeholder="Enter name or skill...")

    with col2:
        status_filter = st.selectbox(
            "Filter by Status",
            options=["All"] + list(set(c.status for c in candidates if c.status))
        )

    with col3:
        experience_filter = st.selectbox(
            "Filter by Experience",
            options=["All", "0-2 years", "3-5 years", "6-10 years", "10+ years"]
        )

    # Filter candidates
    filtered_candidates = candidates

    if search_term:
        filtered_candidates = [
            c for c in filtered_candidates
            if search_term.lower() in c.name.lower() or
               search_term.lower() in c.preferred_role.lower() or
               any(search_term.lower() in skill.skill.lower() for skill in c.skills)
        ]

    if status_filter != "All":
        filtered_candidates = [c for c in filtered_candidates if c.status == status_filter]

    if experience_filter != "All":
        if experience_filter == "0-2 years":
            filtered_candidates = [c for c in filtered_candidates if c.experience_years <= 2]
        elif experience_filter == "3-5 years":
            filtered_candidates = [c for c in filtered_candidates if 3 <= c.experience_years <= 5]
        elif experience_filter == "6-10 years":
            filtered_candidates = [c for c in filtered_candidates if 6 <= c.experience_years <= 10]
        elif experience_filter == "10+ years":
            filtered_candidates = [c for c in filtered_candidates if c.experience_years > 10]

    st.write(f"Showing {len(filtered_candidates)} of {len(candidates)} candidates")

    # Candidates table
    if filtered_candidates:
        # Create DataFrame for better display
        candidate_data = []
        for candidate in filtered_candidates:
            # Get best project match
            best_match = 0
            best_project = "N/A"
            if candidate.project_matches:
                best_match = max(candidate.project_matches.values())
                best_project_id = max(candidate.project_matches.keys(), key=lambda k: candidate.project_matches[k])
                project = st.session_state.matcher.project_requirements.get(best_project_id)
                if project:
                    best_project = project.name[:30] + "..." if len(project.name) > 30 else project.name

            candidate_data.append({
                "Name": candidate.name,
                "Role": candidate.preferred_role,
                "Experience": f"{candidate.experience_years} years",
                "Location": candidate.location,
                "Status": candidate.status,
                "Best Match": f"{best_match:.1f}%",
                "Best Project": best_project,
                "Application Date": candidate.application_date.strftime("%Y-%m-%d")
            })

        df = pd.DataFrame(candidate_data)

        # Display table with selection
        selected_indices = st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            on_select="rerun",
            selection_mode="single-row"
        )

        # Handle row selection
        if selected_indices and len(selected_indices["selection"]["rows"]) > 0:
            selected_idx = selected_indices["selection"]["rows"][0]
            selected_candidate = filtered_candidates[selected_idx]

            col1, col2 = st.columns([3, 1])
            with col2:
                if st.button("View Candidate Details", type="primary"):
                    st.session_state.selected_candidate = selected_candidate.id
                    st.session_state.current_view = "candidate_details"
                    st.rerun()

def render_candidate_details():
    """Render the candidate details view with radar charts and matching scores."""
    if not st.session_state.selected_candidate:
        st.error("No candidate selected")
        return

    candidate = st.session_state.matcher.candidate_profiles.get(st.session_state.selected_candidate)
    if not candidate:
        st.error("Candidate not found")
        return

    # Back button
    if st.button("← Back to Candidates List"):
        st.session_state.current_view = "candidates_list"
        st.session_state.selected_candidate = None
        st.rerun()

    # Candidate header
    st.markdown(f'<div class="main-header">{candidate.name}</div>', unsafe_allow_html=True)
    st.markdown(f"**{candidate.preferred_role}** | Applied: {candidate.application_date.strftime('%Y-%m-%d')}")

    # Basic metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Experience", f"{candidate.experience_years} years")
    with col2:
        st.metric("Status", candidate.status)
    with col3:
        st.metric("Location", candidate.location)
    with col4:
        if candidate.salary_expectation:
            st.metric("Salary Expectation", candidate.salary_expectation)

    # Candidate information tabs
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["👤 Profile", "📊 Skills Radar", "🎯 Project Matches", "💼 Experience", "📋 Projects"])

    with tab1:
        st.markdown('<div class="subsection-header">Candidate Profile</div>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Contact Information:**")
            st.write(f"📧 {candidate.email}")
            st.write(f"📞 {candidate.phone}")
            st.write(f"🔗 [LinkedIn]({candidate.linkedin})" if candidate.linkedin else "")
            st.write(f"💻 [GitHub]({candidate.github})" if candidate.github else "")
            st.write(f"🌐 [Portfolio]({candidate.portfolio})" if candidate.portfolio else "")

            st.markdown("**Application Details:**")
            st.write(f"**Preferred Role:** {candidate.preferred_role}")
            st.write(f"**Remote Work:** {'Yes' if candidate.remote_work else 'No'}")
            st.write(f"**Application Date:** {candidate.application_date.strftime('%Y-%m-%d')}")

        with col2:
            st.markdown("**Education:**")
            st.write(candidate.education)

            if candidate.certifications:
                st.markdown("**Certifications:**")
                for cert in candidate.certifications:
                    st.write(f"• {cert}")

            if candidate.interested_projects:
                st.markdown("**Interested Projects:**")
                for project in candidate.interested_projects:
                    st.write(f"• {project}")

    with tab2:
        st.markdown('<div class="subsection-header">📊 Skills Proficiency Radar Chart</div>', unsafe_allow_html=True)

        if candidate.skill_radar_data:
            # Create radar chart
            categories = list(candidate.skill_radar_data.keys())
            values = list(candidate.skill_radar_data.values())

            fig = go.Figure()

            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name=candidate.name,
                line_color='rgb(31, 119, 180)',
                fillcolor='rgba(31, 119, 180, 0.3)'
            ))

            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                title="Skill Proficiency by Category",
                height=500
            )

            st.plotly_chart(fig, use_container_width=True)

            # Skills breakdown
            st.markdown('<div class="subsection-header">Detailed Skills</div>', unsafe_allow_html=True)

            if candidate.skills:
                for skill in sorted(candidate.skills, key=lambda x: x.proficiency, reverse=True):
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        st.write(skill.skill)
                    with col2:
                        st.progress(skill.proficiency / 100, text=f"{skill.proficiency:.0f}%")
                    with col3:
                        st.write(skill.level)
                    with col4:
                        st.write(f"{skill.years_experience} yrs")
        else:
            st.info("No skill radar data available.")

    with tab3:
        st.markdown('<div class="subsection-header">🎯 Project Matching Scores</div>', unsafe_allow_html=True)

        if candidate.project_matches:
            # Sort projects by match score
            sorted_matches = sorted(
                candidate.project_matches.items(),
                key=lambda x: x[1],
                reverse=True
            )

            st.write("**Project compatibility ranking:**")

            for project_id, score in sorted_matches:
                project = st.session_state.matcher.project_requirements.get(project_id)
                if project:
                    col1, col2, col3 = st.columns([4, 1, 1])

                    with col1:
                        st.write(f"**{project.name}**")
                        st.caption(f"{project.company} | {project.status}")

                    with col2:
                        score_class = "high" if score >= 80 else "medium" if score >= 60 else "low"
                        st.markdown(f'<div class="match-score-{score_class}">{score:.1f}%</div>', unsafe_allow_html=True)

                    with col3:
                        if st.button("View Project", key=f"project_{project_id}_from_candidate"):
                            st.session_state.selected_project = project_id
                            st.session_state.current_view = "project_details"
                            st.rerun()

                    # Match breakdown
                    with st.expander(f"Match Details for {project.name}"):
                        # Calculate detailed scores
                        skills_score = st.session_state.matcher._calculate_skills_match(candidate.skills, project.required_skills)
                        experience_score = st.session_state.matcher._calculate_experience_match(candidate.experience_years, project.experience_level)
                        location_score = st.session_state.matcher._calculate_location_match(candidate.location, project.location_preference)

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Skills Match", f"{skills_score:.1f}%")
                        with col2:
                            st.metric("Experience Match", f"{experience_score:.1f}%")
                        with col3:
                            st.metric("Location Match", f"{location_score:.1f}%")
        else:
            st.info("No project matches calculated yet.")

    with tab4:
        st.markdown('<div class="subsection-header">Professional Experience</div>', unsafe_allow_html=True)
        st.write(f"**Total Experience:** {candidate.experience_years} years")

        # This would be enhanced with detailed work history parsing
        st.info("Detailed work history would be displayed here with enhanced parsing.")

    with tab5:
        st.markdown('<div class="subsection-header">Portfolio Projects</div>', unsafe_allow_html=True)

        if candidate.projects:
            for project in candidate.projects:
                with st.expander(project.get("name", "Unnamed Project")):
                    st.write(project.get("description", "No description available"))
                    if project.get("technologies"):
                        st.write(f"**Technologies:** {', '.join(project['technologies'])}")
        else:
            st.info("No portfolio projects listed.")

if __name__ == "__main__":
    main()
