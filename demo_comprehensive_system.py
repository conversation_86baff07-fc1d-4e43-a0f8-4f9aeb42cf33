#!/usr/bin/env python3
"""
Demo script for the Comprehensive Gemini Talent Matching System v2.0

This script demonstrates the complete functionality of the refactored system
with new data structure, UI components, and advanced matching capabilities.
"""

import asyncio
import sys
import json
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from gemini_talent_matcher_v2 import GeminiTalentMatcherV2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def demo_comprehensive_system():
    """Demonstrate the comprehensive talent matching system."""
    print("🚀 Comprehensive Gemini Talent Matching System v2.0 Demo")
    print("=" * 70)
    
    # Check if API key is available
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key or api_key == "your_google_api_key_here":
        print("⚠️  Google API key not found or not set properly.")
        print("💡 The demo will show system structure and data loading capabilities.")
        print()
    
    try:
        # Initialize the system
        print("🔧 Initializing Comprehensive Talent Matcher...")
        matcher = GeminiTalentMatcherV2(
            working_dir="./demo_comprehensive_cache",
            talent_dir="./talent",
            resume_dir="./resume",
            projects_dir="./projects",
            log_level="INFO"
        )
        print("✅ System initialized successfully!\n")
        
        # Load all data from files
        print("📂 Loading data from directory structure...")
        stats = matcher.load_all_data()
        
        print(f"📊 Data Loading Results:")
        print(f"   • Talent Profiles: {stats['talents_loaded']}")
        print(f"   • Candidate Resumes: {stats['candidates_loaded']}")
        print(f"   • Project Requirements: {stats['projects_loaded']}")
        print(f"   • Errors: {stats['errors']}")
        print()
        
        if stats['talents_loaded'] == 0 and stats['candidates_loaded'] == 0 and stats['projects_loaded'] == 0:
            print("⚠️  No data files found. Please ensure data files exist in the directories.")
            return False
        
        # Update candidate project matches
        print("🔄 Calculating candidate-project matching scores...")
        matcher.update_candidate_project_matches()
        print("✅ Matching scores calculated!\n")
        
        # Demonstrate talent profiles with OKR
        if matcher.talent_profiles:
            print("👥 TALENT PROFILES WITH OKR FRAMEWORK")
            print("-" * 50)
            
            for talent_id, talent in list(matcher.talent_profiles.items())[:2]:  # Show first 2
                print(f"📋 {talent.name} - {talent.title}")
                print(f"   Company: {talent.current_company}")
                print(f"   Experience: {talent.experience_years} years")
                print(f"   Location: {talent.location}")
                print(f"   Availability: {talent.availability}")
                
                # Show top skills
                if talent.skills:
                    top_skills = sorted(talent.skills, key=lambda x: x.proficiency, reverse=True)[:3]
                    skills_text = ", ".join([f"{s.skill} ({s.level})" for s in top_skills])
                    print(f"   Top Skills: {skills_text}")
                
                # Show OKR objectives
                if talent.okr_objectives:
                    print(f"   🎯 Career Objectives (OKR):")
                    for i, okr in enumerate(talent.okr_objectives[:2], 1):  # Show first 2
                        print(f"      {i}. {okr.objective}")
                        print(f"         Progress: {okr.progress:.1f}% | Status: {okr.status}")
                        if okr.key_results:
                            print(f"         Key Results: {len(okr.key_results)} defined")
                
                print()
        
        # Demonstrate candidate profiles with skill assessment
        if matcher.candidate_profiles:
            print("📄 CANDIDATE PROFILES WITH SKILL RADAR")
            print("-" * 50)
            
            for candidate_id, candidate in list(matcher.candidate_profiles.items())[:2]:  # Show first 2
                print(f"📋 {candidate.name} - {candidate.preferred_role}")
                print(f"   Email: {candidate.email}")
                print(f"   Experience: {candidate.experience_years} years")
                print(f"   Location: {candidate.location}")
                print(f"   Status: {candidate.status}")
                print(f"   Application Date: {candidate.application_date.strftime('%Y-%m-%d')}")
                
                # Show skill radar data
                if candidate.skill_radar_data:
                    print(f"   📊 Skill Proficiency by Category:")
                    for category, score in candidate.skill_radar_data.items():
                        if score > 0:
                            print(f"      • {category}: {score:.1f}%")
                
                # Show project matches
                if candidate.project_matches:
                    print(f"   🎯 Project Matching Scores:")
                    sorted_matches = sorted(candidate.project_matches.items(), key=lambda x: x[1], reverse=True)
                    for project_id, score in sorted_matches[:3]:  # Show top 3 matches
                        project = matcher.project_requirements.get(project_id)
                        if project:
                            print(f"      • {project.name}: {score:.1f}% match")
                
                print()
        
        # Demonstrate project requirements
        if matcher.project_requirements:
            print("📋 PROJECT REQUIREMENTS WITH TEAM COMPOSITION")
            print("-" * 50)
            
            for project_id, project in list(matcher.project_requirements.items())[:2]:  # Show first 2
                print(f"📋 {project.name}")
                print(f"   Company: {project.company}")
                print(f"   Status: {project.status} | Priority: {project.priority}")
                print(f"   Timeline: {project.start_date.strftime('%Y-%m-%d')} - {project.end_date.strftime('%Y-%m-%d')}")
                print(f"   Budget: {project.budget}")
                
                # Show current team
                if project.current_team:
                    print(f"   👥 Current Team ({len(project.current_team)} members):")
                    for member in project.current_team[:3]:  # Show first 3
                        print(f"      • {member['role']}: {member['name']}")
                
                # Show open positions
                if project.open_positions:
                    print(f"   💼 Open Positions ({len(project.open_positions)} available):")
                    for position in project.open_positions[:2]:  # Show first 2
                        print(f"      • {position['title']}")
                        if position.get('salary_range'):
                            print(f"        Salary: {position['salary_range']}")
                
                # Show required skills
                if project.required_skills:
                    skills_text = ", ".join(project.required_skills[:5])  # Show first 5
                    print(f"   🔧 Required Skills: {skills_text}")
                
                print()
        
        # Demonstrate matching algorithm
        if matcher.candidate_profiles and matcher.project_requirements:
            print("🎯 ADVANCED MATCHING DEMONSTRATION")
            print("-" * 50)
            
            # Get first candidate and project for demo
            candidate = list(matcher.candidate_profiles.values())[0]
            project = list(matcher.project_requirements.values())[0]
            
            print(f"Matching: {candidate.name} → {project.name}")
            
            # Calculate detailed match scores
            overall_score = matcher.calculate_candidate_project_match(candidate, project)
            skills_score = matcher._calculate_skills_match(candidate.skills, project.required_skills)
            experience_score = matcher._calculate_experience_match(candidate.experience_years, project.experience_level)
            location_score = matcher._calculate_location_match(candidate.location, project.location_preference)
            
            print(f"📊 Detailed Matching Scores:")
            print(f"   • Overall Match: {overall_score:.1f}%")
            print(f"   • Skills Match: {skills_score:.1f}%")
            print(f"   • Experience Match: {experience_score:.1f}%")
            print(f"   • Location Match: {location_score:.1f}%")
            print()
        
        # Show system statistics
        system_stats = matcher.get_system_stats()
        print("📊 SYSTEM STATISTICS")
        print("-" * 50)
        print(json.dumps(system_stats, indent=2))
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main demo function."""
    success = await demo_comprehensive_system()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Comprehensive Talent Matching System v2.0 demo completed successfully!")
        print()
        print("🌟 Key Features Demonstrated:")
        print("   ✅ New directory structure (talent/, resume/, projects/)")
        print("   ✅ Enhanced data parsing from .txt files")
        print("   ✅ OKR framework for talent career planning")
        print("   ✅ Skill radar data for candidate assessment")
        print("   ✅ Advanced project-candidate matching algorithm")
        print("   ✅ Comprehensive UI components ready for Streamlit")
        print()
        print("🚀 Next Steps:")
        print("   1. Run the Streamlit UI: streamlit run streamlit_comprehensive_ui.py")
        print("   2. Explore all views: Projects, Talents, Candidates")
        print("   3. Test the matching and scoring features")
        print("   4. Add your own data files to the directories")
    else:
        print("⚠️  Demo completed with limitations.")
        print("💡 Check the error messages above and ensure data files exist.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
